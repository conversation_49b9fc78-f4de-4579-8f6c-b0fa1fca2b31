import { useAuth } from "@/hooks/use-auth";
import { SlotMachine } from "@/components/game/slot-machine";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Crown, LogOut, Settings, TrendingUp, Coins, Trophy, Zap } from "lucide-react";
import { useState } from "react";
import { useLocation } from "wouter";
import { useIsMobile } from "@/hooks/use-mobile";
import { AudioManager } from "@/components/AudioManager";

export default function HomePage() {
  const { user, logoutMutation } = useAuth();
  const [showSettings, setShowSettings] = useState(false);
  const [, setLocation] = useLocation();
  const isMobile = useIsMobile();
  const [audioSettings, setAudioSettings] = useState({
    soundEffects: 75,
    backgroundMusic: 50,
    autoPlay: false,
    fastSpin: false
  });
  const [soundEffectsRef, setSoundEffectsRef] = useState<{
    playSpinSound: () => void;
    playWinSound: () => void;
  } | null>(null);

  if (!user) {
    return null; // ProtectedRoute will handle redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-deep-navy via-gray-900 to-purple-900 relative overflow-hidden">
      {/* 🎵 Casino Background Music */}
      <AudioManager
        isGameActive={true}
        onSoundEffectsRef={setSoundEffectsRef}
      />

      {/* Animated Background */}
      <div className="fixed inset-0 opacity-30 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-casino-purple rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-casino-red rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-96 h-96 bg-casino-gold rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Header */}
      <header className={`relative z-10 glass-card border-b border-casino-gold/30 ${isMobile ? 'mobile-header' : 'p-6'}`}>
        <div className="max-w-7xl mx-auto">
          {isMobile ? (
            // Mobile Header Layout
            <div className="flex flex-col space-y-4">
              {/* Top Row: Title and User Profile */}
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-casino-gold to-casino-red rounded-xl flex items-center justify-center">
                    <Crown className="w-5 h-5 text-white" />
                  </div>
                  <h1 className="mobile-title font-orbitron font-bold">
                    VEGAS ACE SLOTS
                  </h1>
                </div>
                <button
                  onClick={() => setLocation("/profile")}
                  className="w-10 h-10 bg-casino-purple rounded-full flex items-center justify-center hover:bg-casino-purple/80 transition-colors cursor-pointer border-2 border-transparent hover:border-casino-gold/50"
                >
                  <span className="text-white font-bold">
                    {user.name.charAt(0).toUpperCase()}
                  </span>
                </button>
              </div>

              {/* Bottom Row: Balance */}
              <div className="text-center">
                <p className="text-xs text-gray-400 uppercase tracking-wide">Balance</p>
                <p className="text-2xl font-bold text-casino-gold">
                  ${parseFloat(user.balance).toFixed(2)}
                </p>
              </div>
            </div>
          ) : (
            // Desktop Header Layout
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-casino-gold to-casino-red rounded-xl flex items-center justify-center">
                  <Crown className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-orbitron font-bold text-transparent bg-clip-text bg-gradient-to-r from-casino-gold via-casino-red to-casino-purple">
                    VEGAS ACE SLOTS
                  </h1>
                  <p className="text-sm text-gray-300">Premium Casino Experience</p>
                </div>
              </div>

              <div className="flex items-center space-x-6">
                {/* User Balance */}
                <Card className="glass-card border-casino-gold/30">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-xs text-gray-400 uppercase tracking-wide">Balance</p>
                      <p className="text-2xl font-bold text-casino-gold">
                        ${parseFloat(user.balance).toFixed(2)}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* User Profile */}
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setLocation("/profile")}
                    className="w-10 h-10 bg-casino-purple rounded-full flex items-center justify-center hover:bg-casino-purple/80 transition-colors cursor-pointer border-2 border-transparent hover:border-casino-gold/50"
                  >
                    <span className="text-white font-bold">
                      {user.name.charAt(0).toUpperCase()}
                    </span>
                  </button>
                  <div className="hidden md:block">
                    <p className="font-semibold text-white">{user.name}</p>
                    <p className="text-xs text-gray-400">Premium Player</p>
                  </div>
                </div>

                {/* Settings & Logout */}
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowSettings(!showSettings)}
                    className="glass-card border-casino-gold/30 hover:bg-casino-gold/20"
                  >
                    <Settings className="w-5 h-5 text-casino-gold" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => logoutMutation.mutate()}
                    className="glass-card border-casino-red/30 hover:bg-casino-red/20"
                    disabled={logoutMutation.isPending}
                  >
                    <LogOut className="w-5 h-5 text-casino-red" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Game Area */}
      <main className={`relative z-10 ${isMobile ? 'p-4' : 'p-6'}`}>
        <div className="max-w-7xl mx-auto">
          {isMobile ? (
            // Mobile Layout
            <div className="space-y-4">
              {/* Progressive Jackpot - Mobile */}
              <div className="mobile-jackpot">
                <div className="mobile-jackpot-label">Progressive Jackpot</div>
                <div className="mobile-jackpot-amount">$127,834.22</div>
                <div className="mobile-jackpot-subtitle">Growing every spin!</div>
              </div>

              {/* Ways to Win */}
              <div className="mobile-ways-to-win">1024 Ways to Win</div>

              {/* Slot Machine */}
              <SlotMachine user={user} />

              {/* Mobile Stats - Collapsible */}
              <div className="space-y-3">
                <Card className="glass-card border-casino-gold/30">
                  <CardContent className="p-4">
                    <h3 className="text-sm font-orbitron font-bold text-casino-gold mb-3 flex items-center">
                      <TrendingUp className="w-4 h-4 mr-2" />
                      Session Stats
                    </h3>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-300">Total Spins</span>
                        <span className="font-semibold text-white">{user.totalSpins}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Total Wins</span>
                        <span className="font-semibold text-green-400">
                          ${parseFloat(user.totalWins).toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Member Since</span>
                        <span className="font-semibold text-white">
                          {new Date(user.joinDate).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Status</span>
                        <span className="font-semibold text-casino-gold">Active</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card border-casino-purple/30">
                  <CardContent className="p-4">
                    <h3 className="text-sm font-orbitron font-bold text-casino-purple mb-3 flex items-center">
                      <Zap className="w-4 h-4 mr-2" />
                      RNG Status
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-gray-300">Crypto-Secure RNG</span>
                      </div>
                      <div className="text-xs text-gray-400">
                        Current RTP: <span className="text-casino-gold font-semibold">94.2%</span>
                      </div>
                      <div className="text-xs text-gray-400">
                        Fair Gaming Certified ✓
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            // Desktop Layout
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
              {/* Left Sidebar - Statistics */}
              <div className="lg:col-span-3 space-y-4">
                <Card className="glass-card border-casino-gold/30">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-orbitron font-bold text-casino-gold mb-4 flex items-center">
                      <TrendingUp className="w-5 h-5 mr-2" />
                      Session Stats
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-300">Total Spins</span>
                        <span className="font-semibold text-white">{user.totalSpins}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Total Wins</span>
                        <span className="font-semibold text-green-400">
                          ${parseFloat(user.totalWins).toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Member Since</span>
                        <span className="font-semibold text-white">
                          {new Date(user.joinDate).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Status</span>
                        <span className="font-semibold text-casino-gold">Active</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card border-casino-purple/30">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-orbitron font-bold text-casino-purple mb-4 flex items-center">
                      <Zap className="w-5 h-5 mr-2" />
                      RNG Status
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-sm text-gray-300">Crypto-Secure RNG</span>
                      </div>
                      <div className="text-xs text-gray-400">
                        Current RTP: <span className="text-casino-gold font-semibold">94.2%</span>
                      </div>
                      <div className="text-xs text-gray-400">
                        Fair Gaming Certified ✓
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card border-casino-red/30">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-orbitron font-bold text-casino-red mb-4 flex items-center">
                      <Trophy className="w-5 h-5 mr-2" />
                      Progressive Jackpot
                    </h3>
                    <div className="text-center">
                      <div className="text-2xl font-black text-casino-gold animate-pulse">
                        $127,834.22
                      </div>
                      <div className="mt-2 h-2 bg-gray-700 rounded-full overflow-hidden">
                        <div className="h-full w-3/4 bg-gradient-to-r from-casino-gold via-casino-red to-casino-purple rounded-full animate-pulse"></div>
                      </div>
                      <p className="text-xs text-gray-400 mt-2">Growing every spin!</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Center - Slot Machine */}
              <div className="lg:col-span-9">
                <SlotMachine user={user} />
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Settings Modal */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <Card className="glass-card border-casino-gold/30 max-w-md mx-4">
            <CardContent className="p-8">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-casino-gold">Game Settings</h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowSettings(false)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </Button>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm text-gray-300 mb-2">🎵 Casino Jazz Music</label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={audioSettings.backgroundMusic}
                      onChange={(e) => setAudioSettings(prev => ({
                        ...prev,
                        backgroundMusic: parseInt(e.target.value)
                      }))}
                      className="flex-1 accent-casino-gold"
                    />
                    <span className="text-casino-gold font-bold">{audioSettings.backgroundMusic}%</span>
                  </div>
                  <p className="text-xs text-gray-400 mt-1">Authentic casino jazz piano music</p>
                </div>

                <div>
                  <label className="block text-sm text-gray-300 mb-2">🎰 Sound Effects</label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={audioSettings.soundEffects}
                      onChange={(e) => setAudioSettings(prev => ({
                        ...prev,
                        soundEffects: parseInt(e.target.value)
                      }))}
                      className="flex-1 accent-casino-gold"
                    />
                    <span className="text-casino-gold font-bold">{audioSettings.soundEffects}%</span>
                  </div>
                  <p className="text-xs text-gray-400 mt-1">Spin and win sound effects</p>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-gray-300">Auto-play</span>
                    <p className="text-xs text-gray-400">Automatically spin reels</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={audioSettings.autoPlay}
                      onChange={(e) => setAudioSettings(prev => ({
                        ...prev,
                        autoPlay: e.target.checked
                      }))}
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-casino-gold"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-gray-300">Fast Spin</span>
                    <p className="text-xs text-gray-400">Faster reel animations</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={audioSettings.fastSpin}
                      onChange={(e) => setAudioSettings(prev => ({
                        ...prev,
                        fastSpin: e.target.checked
                      }))}
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-casino-gold"></div>
                  </label>
                </div>

                <div className="pt-4 border-t border-gray-600">
                  <p className="text-xs text-gray-400 text-center">
                    🎼 Background music: Casino Jazz Piano Playlist
                  </p>
                  <p className="text-xs text-gray-400 text-center mt-1">
                    Perfect for poker, blackjack, roulette & slots
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Footer */}
      <footer className="relative z-10 glass-card border-t border-casino-gold/30 p-4 mt-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <span>© 2024 Vegas Ace Slots</span>
              <span>•</span>
              <span>Responsible Gaming</span>
              <span>•</span>
              <span>Privacy Policy</span>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <span>🔒 SSL Secured</span>
              <span>•</span>
              <span>🎯 18+ Only</span>
              <span>•</span>
              <span>🎰 Licensed Gaming</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

