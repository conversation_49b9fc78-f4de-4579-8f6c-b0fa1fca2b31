/**
 * RNGEngine - Crypto-secure Random Number Generation
 * Provides cryptographically secure random numbers for slot machine operations
 */

export class RNGEngine {
  private seed: string;
  private counter: number;
  private entropy: Uint32Array;

  constructor(seed?: string) {
    this.seed = seed || this.generateSeed();
    this.counter = 0;
    this.entropy = new Uint32Array(4);
    this.reseed();
  }

  /**
   * Generate a cryptographically secure seed
   */
  private generateSeed(): string {
    if (window.crypto && window.crypto.getRandomValues) {
      const array = new Uint8Array(32);
      window.crypto.getRandomValues(array);
      return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }
    
    // Fallback for environments without crypto.getRandomValues
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  /**
   * Reseed the RNG with fresh entropy
   */
  private reseed(): void {
    if (window.crypto && window.crypto.getRandomValues) {
      window.crypto.getRandomValues(this.entropy);
    } else {
      // Fallback entropy generation
      for (let i = 0; i < 4; i++) {
        this.entropy[i] = Math.random() * 0xffffffff;
      }
    }
  }

  /**
   * Generate next random number using Xorshift128 algorithm
   * Returns a value between 0 and 1 (exclusive)
   */
  public next(): number {
    this.counter++;
    
    // Reseed every 1000 calls for additional security
    if (this.counter % 1000 === 0) {
      this.reseed();
    }

    // Xorshift128 algorithm for high-quality pseudo-random numbers
    let t = this.entropy[3];
    this.entropy[3] = this.entropy[2];
    this.entropy[2] = this.entropy[1];
    this.entropy[1] = this.entropy[0];
    
    t ^= t << 11;
    t ^= t >>> 8;
    this.entropy[0] = t ^ this.entropy[0] ^ (this.entropy[0] >>> 19);
    
    // Normalize to [0, 1) range
    return (this.entropy[0] >>> 0) / 0xffffffff;
  }

  /**
   * Generate random integer between min and max (inclusive)
   */
  public nextInt(min: number, max: number): number {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }

  /**
   * Generate random number with specific distribution
   */
  public nextWeighted(weights: number[]): number {
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = this.next() * totalWeight;
    
    for (let i = 0; i < weights.length; i++) {
      random -= weights[i];
      if (random <= 0) return i;
    }
    
    return weights.length - 1; // Fallback
  }

  /**
   * Generate random boolean with specified probability
   */
  public nextBoolean(probability: number = 0.5): boolean {
    return this.next() < probability;
  }

  /**
   * Generate array of random numbers
   */
  public nextArray(length: number): number[] {
    const result: number[] = [];
    for (let i = 0; i < length; i++) {
      result.push(this.next());
    }
    return result;
  }

  /**
   * Shuffle array using Fisher-Yates algorithm
   */
  public shuffle<T>(array: T[]): T[] {
    const shuffled = [...array];
    
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = this.nextInt(0, i);
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    
    return shuffled;
  }

  /**
   * Get current seed for reproducibility
   */
  public getSeed(): string {
    return this.seed;
  }

  /**
   * Get current counter value
   */
  public getCounter(): number {
    return this.counter;
  }

  /**
   * Reset RNG with new seed
   */
  public reset(newSeed?: string): void {
    this.seed = newSeed || this.generateSeed();
    this.counter = 0;
    this.reseed();
  }

  /**
   * Generate audit trail for transparency
   */
  public getAuditInfo(): {
    seed: string;
    counter: number;
    entropy: number[];
    timestamp: number;
  } {
    return {
      seed: this.seed,
      counter: this.counter,
      entropy: Array.from(this.entropy),
      timestamp: Date.now(),
    };
  }

  /**
   * Verify RNG state for fairness validation
   */
  public verify(): boolean {
    // Basic validation checks
    if (!this.seed || this.seed.length < 10) return false;
    if (this.counter < 0) return false;
    if (!this.entropy || this.entropy.length !== 4) return false;
    
    return true;
  }
}

// Export singleton instance for consistent usage
export const gameRNG = new RNGEngine();
