/**
 * 🎯 DAILY & VIP REWARD SYSTEM
 * Manages daily free spins and VIP tier bonuses
 */

import { User } from "@shared/schema";

export interface DailyReward {
  day: number;
  claimed: boolean;
  reward: {
    type: 'free_spin' | 'credits' | 'multiplier';
    amount: number;
    description: string;
  };
}

export interface VipTier {
  level: 'bronze' | 'silver' | 'gold';
  name: string;
  pointsRequired: number;
  weeklyBonus: number; // BDT amount
  color: string;
  benefits: string[];
}

export interface VipProgress {
  currentPoints: number;
  currentTier: VipTier;
  nextTier: VipTier | null;
  pointsToNext: number;
  weeklyBonusAvailable: boolean;
  lastWeeklyClaimDate: Date | null;
}

export class DailyVipSystem {
  private readonly VIP_TIERS: VipTier[] = [
    {
      level: 'bronze',
      name: 'Bronze Player',
      pointsRequired: 0,
      weeklyBonus: 10,
      color: '#CD7F32',
      benefits: [
        '10 BDT Weekly Bonus',
        'Priority Support',
        'Special Animations'
      ]
    },
    {
      level: 'silver',
      name: 'Silver Elite',
      pointsRequired: 1000,
      weeklyBonus: 20,
      color: '#C0C0C0',
      benefits: [
        '20 BDT Weekly Bonus',
        'Exclusive Sound Effects',
        'Enhanced Celebrations',
        'Priority Support'
      ]
    },
    {
      level: 'gold',
      name: 'Gold Legend',
      pointsRequired: 5000,
      weeklyBonus: 50,
      color: '#FFD700',
      benefits: [
        '50 BDT Weekly Bonus',
        'Legendary Animations',
        'Exclusive Themes',
        'VIP Support Channel',
        'Special Recognition'
      ]
    }
  ];

  /**
   * 🎁 Get daily rewards for new user (10 days)
   */
  public generateDailyRewards(): DailyReward[] {
    return [
      {
        day: 1,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 1,
          description: 'Welcome! 1 Free Spin'
        }
      },
      {
        day: 2,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 1,
          description: 'Day 2 Bonus - 1 Free Spin'
        }
      },
      {
        day: 3,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 1,
          description: 'Day 3 Bonus - 1 Free Spin'
        }
      },
      {
        day: 4,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 1,
          description: 'Day 4 Bonus - 1 Free Spin'
        }
      },
      {
        day: 5,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 1,
          description: 'Day 5 Bonus - 1 Free Spin'
        }
      },
      {
        day: 6,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 1,
          description: 'Day 6 Bonus - 1 Free Spin'
        }
      },
      {
        day: 7,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 2,
          description: 'Week Complete! 2 Free Spins'
        }
      },
      {
        day: 8,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 1,
          description: 'Day 8 Bonus - 1 Free Spin'
        }
      },
      {
        day: 9,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 1,
          description: 'Day 9 Bonus - 1 Free Spin'
        }
      },
      {
        day: 10,
        claimed: false,
        reward: {
          type: 'free_spin',
          amount: 3,
          description: 'Grand Finale! 3 Free Spins'
        }
      }
    ];
  }

  /**
   * 🎯 Check if user can claim daily reward
   */
  public canClaimDailyReward(user: User): {
    canClaim: boolean;
    currentDay: number;
    reward: DailyReward | null;
    daysRemaining: number;
  } {
    const signupDate = new Date(user.createdAt);
    const now = new Date();
    const daysSinceSignup = Math.floor((now.getTime() - signupDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // Daily rewards are only for first 10 days
    if (daysSinceSignup >= 10) {
      return {
        canClaim: false,
        currentDay: 11,
        reward: null,
        daysRemaining: 0
      };
    }
    
    const currentDay = daysSinceSignup + 1;
    const dailyRewards = this.generateDailyRewards();
    const todayReward = dailyRewards[currentDay - 1];
    
    // Check if already claimed today
    const lastClaimDate = user.lastDailyClaimDate ? new Date(user.lastDailyClaimDate) : null;
    const today = new Date().toDateString();
    const alreadyClaimed = lastClaimDate && lastClaimDate.toDateString() === today;
    
    return {
      canClaim: !alreadyClaimed && currentDay <= 10,
      currentDay,
      reward: todayReward || null,
      daysRemaining: Math.max(0, 10 - currentDay)
    };
  }

  /**
   * 🎁 Claim daily reward
   */
  public claimDailyReward(user: User): {
    success: boolean;
    reward: DailyReward | null;
    newFreeSpins: number;
    message: string;
  } {
    const claimInfo = this.canClaimDailyReward(user);
    
    if (!claimInfo.canClaim || !claimInfo.reward) {
      return {
        success: false,
        reward: null,
        newFreeSpins: 0,
        message: 'No daily reward available'
      };
    }
    
    const reward = claimInfo.reward;
    const freeSpinsToAdd = reward.reward.type === 'free_spin' ? reward.reward.amount : 0;
    
    return {
      success: true,
      reward,
      newFreeSpins: freeSpinsToAdd,
      message: `Day ${claimInfo.currentDay} reward claimed! ${reward.reward.description}`
    };
  }

  /**
   * 🏆 Calculate VIP progress
   */
  public calculateVipProgress(user: User): VipProgress {
    const currentPoints = user.vipPoints || 0;
    const currentTier = this.getCurrentVipTier(currentPoints);
    const nextTier = this.getNextVipTier(currentTier.level);
    const pointsToNext = nextTier ? nextTier.pointsRequired - currentPoints : 0;
    
    // Check weekly bonus availability
    const lastWeeklyClaimDate = user.lastWeeklyBonusDate ? new Date(user.lastWeeklyBonusDate) : null;
    const weeklyBonusAvailable = this.isWeeklyBonusAvailable(lastWeeklyClaimDate);
    
    return {
      currentPoints,
      currentTier,
      nextTier,
      pointsToNext: Math.max(0, pointsToNext),
      weeklyBonusAvailable,
      lastWeeklyClaimDate
    };
  }

  /**
   * 🎯 Get current VIP tier based on points
   */
  public getCurrentVipTier(points: number): VipTier {
    for (let i = this.VIP_TIERS.length - 1; i >= 0; i--) {
      if (points >= this.VIP_TIERS[i].pointsRequired) {
        return this.VIP_TIERS[i];
      }
    }
    return this.VIP_TIERS[0]; // Default to bronze
  }

  /**
   * 🎯 Get next VIP tier
   */
  public getNextVipTier(currentLevel: string): VipTier | null {
    const currentIndex = this.VIP_TIERS.findIndex(tier => tier.level === currentLevel);
    if (currentIndex === -1 || currentIndex === this.VIP_TIERS.length - 1) {
      return null; // Already at max tier
    }
    return this.VIP_TIERS[currentIndex + 1];
  }

  /**
   * 🎁 Check if weekly bonus is available
   */
  public isWeeklyBonusAvailable(lastClaimDate: Date | null): boolean {
    if (!lastClaimDate) return true;
    
    const now = new Date();
    const daysSinceLastClaim = Math.floor((now.getTime() - lastClaimDate.getTime()) / (1000 * 60 * 60 * 24));
    
    return daysSinceLastClaim >= 7;
  }

  /**
   * 🏆 Claim weekly VIP bonus
   */
  public claimWeeklyBonus(user: User): {
    success: boolean;
    bonusAmount: number;
    newBalance: string;
    tierName: string;
    message: string;
  } {
    const vipProgress = this.calculateVipProgress(user);
    
    if (!vipProgress.weeklyBonusAvailable) {
      return {
        success: false,
        bonusAmount: 0,
        newBalance: user.balance,
        tierName: vipProgress.currentTier.name,
        message: 'Weekly bonus not available yet'
      };
    }
    
    const bonusAmount = vipProgress.currentTier.weeklyBonus;
    const currentBalance = parseFloat(user.balance);
    const newBalance = (currentBalance + bonusAmount).toFixed(2);
    
    return {
      success: true,
      bonusAmount,
      newBalance,
      tierName: vipProgress.currentTier.name,
      message: `${vipProgress.currentTier.name} weekly bonus claimed! +${bonusAmount} BDT`
    };
  }

  /**
   * 🎯 Add VIP points for activity
   */
  public addVipPoints(user: User, betAmount: number, won: boolean): number {
    let pointsToAdd = Math.floor(betAmount / 10); // 1 point per 10 BDT bet
    
    // Bonus points for wins
    if (won) {
      pointsToAdd += Math.floor(betAmount / 20); // Extra points for wins
    }
    
    // Minimum 1 point for playing
    pointsToAdd = Math.max(1, pointsToAdd);
    
    return pointsToAdd;
  }

  /**
   * 🎯 Get all VIP tiers for display
   */
  public getAllVipTiers(): VipTier[] {
    return [...this.VIP_TIERS];
  }

  /**
   * 🎯 Check for VIP tier upgrade
   */
  public checkTierUpgrade(oldPoints: number, newPoints: number): {
    upgraded: boolean;
    oldTier: VipTier;
    newTier: VipTier;
    message: string;
  } {
    const oldTier = this.getCurrentVipTier(oldPoints);
    const newTier = this.getCurrentVipTier(newPoints);
    
    const upgraded = oldTier.level !== newTier.level;
    
    return {
      upgraded,
      oldTier,
      newTier,
      message: upgraded 
        ? `🎉 VIP Tier Upgraded! Welcome to ${newTier.name}!`
        : ''
    };
  }

  /**
   * 🎯 Get daily login streak bonus
   */
  public calculateLoginStreak(user: User): {
    currentStreak: number;
    bonusMultiplier: number;
    nextMilestone: number;
    message: string;
  } {
    const lastLoginDate = user.lastLoginDate ? new Date(user.lastLoginDate) : null;
    const today = new Date();
    
    let currentStreak = user.loginStreak || 0;
    
    if (lastLoginDate) {
      const daysSinceLastLogin = Math.floor((today.getTime() - lastLoginDate.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysSinceLastLogin === 1) {
        // Consecutive day
        currentStreak++;
      } else if (daysSinceLastLogin > 1) {
        // Streak broken
        currentStreak = 1;
      }
      // Same day = no change
    } else {
      // First login
      currentStreak = 1;
    }
    
    // Calculate bonus multiplier based on streak
    let bonusMultiplier = 1;
    if (currentStreak >= 7) bonusMultiplier = 1.5;
    else if (currentStreak >= 3) bonusMultiplier = 1.2;
    
    // Next milestone
    let nextMilestone = 3;
    if (currentStreak >= 7) nextMilestone = Math.ceil(currentStreak / 7) * 7 + 7;
    else if (currentStreak >= 3) nextMilestone = 7;
    
    let message = '';
    if (currentStreak >= 7) {
      message = `🔥 ${currentStreak} day streak! 50% bonus active!`;
    } else if (currentStreak >= 3) {
      message = `⭐ ${currentStreak} day streak! 20% bonus active!`;
    } else if (currentStreak === 1) {
      message = '👋 Welcome back! Start your streak!';
    }
    
    return {
      currentStreak,
      bonusMultiplier,
      nextMilestone,
      message
    };
  }

  /**
   * 🎯 Get user's daily/VIP summary
   */
  public getUserSummary(user: User): {
    dailyReward: ReturnType<typeof this.canClaimDailyReward>;
    vipProgress: VipProgress;
    loginStreak: ReturnType<typeof this.calculateLoginStreak>;
    totalBenefits: string[];
  } {
    const dailyReward = this.canClaimDailyReward(user);
    const vipProgress = this.calculateVipProgress(user);
    const loginStreak = this.calculateLoginStreak(user);
    
    const totalBenefits = [
      ...vipProgress.currentTier.benefits,
      `${loginStreak.currentStreak} day login streak`,
      dailyReward.daysRemaining > 0 ? `${dailyReward.daysRemaining} daily rewards remaining` : ''
    ].filter(Boolean);
    
    return {
      dailyReward,
      vipProgress,
      loginStreak,
      totalBenefits
    };
  }
}

// Export singleton instance
export const dailyVipSystem = new DailyVipSystem();
