import { User } from "@shared/schema";

// 🧠 Enhanced Psychology Engine for Natural Game Feel
export class EnhancedPsychologyEngine {
  
  // 🎯 Calculate Dynamic Win Chance Based on Player Psychology
  calculateDynamicWinChance(user: User, betAmount: number): number {
    let baseWinChance = 0.25; // 25% base win rate
    
    // 🧠 1. Luck Score Adjustment (Most Important)
    const luckMultiplier = this.getLuckMultiplier(user.luckScore);
    baseWinChance *= luckMultiplier;
    
    // 💰 2. Bet Amount Psychology (Your Request)
    const betMultiplier = this.getBetAmountMultiplier(betAmount);
    baseWinChance *= betMultiplier;
    
    // 🔥 3. Recent Loss Compensation
    const lossCompensation = this.getLossCompensation(user.recentLosses);
    baseWinChance += lossCompensation;
    
    // ❄️ 4. Win Streak Cooling
    const winCooling = this.getWinCooling(user.recentWins);
    baseWinChance -= winCooling;
    
    // 🎰 5. VIP Level Bonus
    const vipBonus = this.getVipBonus(user.vipLevel);
    baseWinChance += vipBonus;
    
    // Ensure win chance stays within reasonable bounds
    return Math.max(0.05, Math.min(0.45, baseWinChance));
  }
  
  // 🧠 Luck Score Multiplier (0-100 score affects wins)
  private getLuckMultiplier(luckScore: number): number {
    if (luckScore <= 20) return 1.3; // Very unlucky, boost them
    if (luckScore <= 40) return 1.15; // Unlucky, slight boost
    if (luckScore <= 60) return 1.0; // Normal luck
    if (luckScore <= 80) return 0.9; // Lucky, slight reduction
    return 0.8; // Very lucky, reduce wins
  }
  
  // 💰 Bet Amount Psychology (Your Specific Request)
  private getBetAmountMultiplier(betAmount: number): number {
    if (betAmount < 50) {
      return 1.2; // Small bets = more frequent wins
    } else if (betAmount >= 50 && betAmount < 200) {
      return 1.0; // Medium bets = normal wins
    } else {
      return 0.8; // Big bets = less frequent but bigger wins
    }
  }
  
  // 🔥 Loss Compensation (Prevent frustration)
  private getLossCompensation(recentLosses: number): number {
    if (recentLosses >= 10) return 0.15; // 15% boost after 10 losses
    if (recentLosses >= 7) return 0.10; // 10% boost after 7 losses
    if (recentLosses >= 5) return 0.05; // 5% boost after 5 losses
    return 0;
  }
  
  // ❄️ Win Streak Cooling (Prevent too many wins)
  private getWinCooling(recentWins: number): number {
    if (recentWins >= 5) return 0.10; // Reduce by 10% after 5 wins
    if (recentWins >= 3) return 0.05; // Reduce by 5% after 3 wins
    return 0;
  }
  
  // 🎰 VIP Level Bonus (Not win chance, just slight edge)
  private getVipBonus(vipLevel: string): number {
    switch (vipLevel) {
      case 'diamond': return 0.03; // 3% bonus
      case 'gold': return 0.02; // 2% bonus
      case 'silver': return 0.01; // 1% bonus
      case 'bronze': return 0; // No bonus
      default: return 0;
    }
  }
  
  // 🎲 Calculate Bonus Round Chance
  calculateBonusChance(user: User, betAmount: number): number {
    let baseBonusChance = 0.08; // 8% base bonus chance
    
    // Higher bets = more bonus chances (Your Request)
    if (betAmount >= 200) {
      baseBonusChance = 0.15; // 15% for big bets
    } else if (betAmount >= 100) {
      baseBonusChance = 0.12; // 12% for medium-high bets
    } else if (betAmount >= 50) {
      baseBonusChance = 0.10; // 10% for medium bets
    }
    
    // Luck score affects bonus too
    if (user.luckScore <= 30) {
      baseBonusChance *= 1.2; // Boost bonus for unlucky players
    }
    
    return baseBonusChance;
  }
  
  // 🎯 Calculate Win Multiplier (How big the win should be)
  calculateWinMultiplier(user: User, betAmount: number, isBonus: boolean): number {
    let baseMultiplier = 2.0; // 2x bet as base win
    
    // 💰 Big bets should have bigger multipliers
    if (betAmount >= 200) {
      baseMultiplier = Math.random() < 0.3 ? 8.0 : 3.0; // 30% chance of 8x, else 3x
    } else if (betAmount >= 100) {
      baseMultiplier = Math.random() < 0.2 ? 5.0 : 2.5; // 20% chance of 5x, else 2.5x
    } else if (betAmount >= 50) {
      baseMultiplier = Math.random() < 0.15 ? 4.0 : 2.0; // 15% chance of 4x, else 2x
    }
    
    // 🎰 Bonus rounds have higher multipliers
    if (isBonus) {
      baseMultiplier *= (1.5 + Math.random() * 2); // 1.5x to 3.5x bonus multiplier
    }
    
    // 🧠 Luck score affects win size
    if (user.luckScore <= 20) {
      baseMultiplier *= 1.3; // Bigger wins for very unlucky players
    }
    
    return baseMultiplier;
  }
  
  // 🎯 Determine if this should be a "Near Miss" (Psychology)
  shouldCreateNearMiss(user: User): boolean {
    // Create near misses for players who haven't won recently
    if (user.recentLosses >= 3 && Math.random() < 0.3) {
      return true; // 30% chance of near miss after 3+ losses
    }
    return false;
  }
  
  // 📈 Update User Psychology After Spin
  updateUserPsychology(user: User, won: boolean, winAmount: number, betAmount: number): Partial<User> {
    const updates: Partial<User> = {};
    
    if (won) {
      // Reset loss streak, increment win streak
      updates.recentLosses = 0;
      updates.recentWins = (user.recentWins || 0) + 1;
      
      // Decrease luck score (they got lucky)
      updates.luckScore = Math.max(0, (user.luckScore || 50) - 5);
      
      // Track biggest win
      if (winAmount > parseFloat(user.biggestWin || "0")) {
        updates.biggestWin = winAmount.toString();
        updates.lastBigWin = new Date();
      }
      
      // Add VIP points based on bet
      updates.vipPoints = (user.vipPoints || 0) + Math.floor(betAmount / 10);
      
    } else {
      // Reset win streak, increment loss streak
      updates.recentWins = 0;
      updates.recentLosses = (user.recentLosses || 0) + 1;
      
      // Increase luck score (they're getting unlucky)
      updates.luckScore = Math.min(100, (user.luckScore || 50) + 3);
      
      // Still add small VIP points for playing
      updates.vipPoints = (user.vipPoints || 0) + 1;
    }
    
    // Update VIP level based on points
    const newVipLevel = this.calculateVipLevel(updates.vipPoints || user.vipPoints || 0);
    if (newVipLevel !== user.vipLevel) {
      updates.vipLevel = newVipLevel;
    }
    
    return updates;
  }
  
  // 🏆 Calculate VIP Level from Points
  private calculateVipLevel(vipPoints: number): string {
    if (vipPoints >= 10000) return 'diamond';
    if (vipPoints >= 5000) return 'gold';
    if (vipPoints >= 1000) return 'silver';
    return 'bronze';
  }
  
  // 🎁 Calculate Daily Login Bonus
  calculateLoginBonus(loginStreak: number): number {
    const bonuses = [50, 75, 100, 150, 200, 300, 500]; // Day 1-7 bonuses
    const dayIndex = Math.min(loginStreak - 1, bonuses.length - 1);
    return bonuses[dayIndex] || 500; // Max 500 BDT for 7+ days
  }
  
  // 🎰 Calculate Weekly VIP Bonus
  calculateWeeklyVipBonus(vipLevel: string): number {
    switch (vipLevel) {
      case 'diamond': return 100; // 100 BDT
      case 'gold': return 50; // 50 BDT
      case 'silver': return 20; // 20 BDT
      case 'bronze': return 10; // 10 BDT
      default: return 10;
    }
  }
  
  // 🎯 Check if user should get free spin today
  shouldGetFreeSpinToday(user: User): boolean {
    const today = new Date().toISOString().split('T')[0];
    const lastFreeSpinDate = user.lastFreeSpinDate;
    
    // New users get 10 free spins (1 per day for 10 days)
    if (!lastFreeSpinDate && user.freeSpinsRemaining > 0) {
      return true;
    }
    
    // Check if it's a new day and they have spins remaining
    if (lastFreeSpinDate !== today && user.freeSpinsRemaining > 0) {
      return true;
    }
    
    return false;
  }
}
