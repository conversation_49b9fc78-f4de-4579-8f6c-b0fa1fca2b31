/**
 * 🎰 CASCADE MANAGER
 * Handles cascading mechanics - cards vanish and new ones drop down
 */

export interface CascadeResult {
  newGrid: string[][];
  cascadeWins: any[];
  totalCascades: number;
  totalWinAmount: number;
}

export class CascadeManager {
  private symbols = ['9', '10', 'J', 'Q', 'K', 'A', 'WILD', 'SCATTER'];

  /**
   * 🎯 Process cascading after initial win
   */
  public async processCascades(
    initialGrid: string[][],
    initialWins: any[],
    cascadeCount: number = 0
  ): Promise<CascadeResult> {
    console.log(`🌊 Starting cascade processing, target cascades: ${cascadeCount}`);
    
    let currentGrid = JSON.parse(JSON.stringify(initialGrid));
    let allWins = [...initialWins];
    let totalCascades = 0;
    let totalWinAmount = 0;

    // Calculate initial win amount
    totalWinAmount += this.calculateWinAmount(initialWins);

    // Process cascades if there are wins and cascades are enabled
    if (initialWins.length > 0 && cascadeCount > 0) {
      for (let cascade = 0; cascade < cascadeCount; cascade++) {
        console.log(`🌊 Processing cascade ${cascade + 1}/${cascadeCount}`);
        
        // Remove winning symbols and drop new ones
        const cascadeResult = await this.performCascade(currentGrid, initialWins);
        currentGrid = cascadeResult.newGrid;
        
        // Check for new wins in cascaded grid
        const newWins = this.findWins(currentGrid);
        
        if (newWins.length > 0) {
          console.log(`🎉 Cascade ${cascade + 1} created ${newWins.length} new wins!`);
          allWins.push(...newWins);
          totalWinAmount += this.calculateWinAmount(newWins);
          totalCascades++;
          
          // Animate the cascade
          await this.animateCascade(cascade + 1);
        } else {
          console.log(`🌊 Cascade ${cascade + 1} ended - no new wins`);
          break;
        }
      }
    }

    return {
      newGrid: currentGrid,
      cascadeWins: allWins,
      totalCascades,
      totalWinAmount
    };
  }

  /**
   * 🌊 Perform single cascade
   */
  private async performCascade(grid: string[][], wins: any[]): Promise<{ newGrid: string[][] }> {
    const newGrid = JSON.parse(JSON.stringify(grid));
    
    // Mark winning positions for removal
    const toRemove: boolean[][] = Array(5).fill(null).map(() => Array(4).fill(false));
    
    wins.forEach(win => {
      win.positions?.forEach((pos: any) => {
        if (pos.reel < 5 && pos.row < 4) {
          toRemove[pos.reel][pos.row] = true;
        }
      });
    });

    // Remove winning symbols and drop remaining symbols down
    for (let reel = 0; reel < 5; reel++) {
      const reelSymbols: string[] = [];
      
      // Collect non-winning symbols from bottom to top
      for (let row = 3; row >= 0; row--) {
        if (!toRemove[reel][row]) {
          reelSymbols.unshift(newGrid[reel][row]);
        }
      }
      
      // Fill the reel: existing symbols at bottom, new symbols at top
      for (let row = 0; row < 4; row++) {
        if (row < reelSymbols.length) {
          newGrid[reel][3 - row] = reelSymbols[reelSymbols.length - 1 - row];
        } else {
          // Generate new symbol for empty positions
          newGrid[reel][3 - row] = this.generateNewSymbol();
        }
      }
    }

    return { newGrid };
  }

  /**
   * 🎲 Generate new symbol for cascade
   */
  private generateNewSymbol(): string {
    // Weighted selection for new symbols
    const weights = {
      '9': 25,
      '10': 22,
      'J': 18,
      'Q': 15,
      'K': 12,
      'A': 8,
      'WILD': 2,
      'SCATTER': 1
    };

    const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const [symbol, weight] of Object.entries(weights)) {
      random -= weight;
      if (random <= 0) {
        return symbol;
      }
    }
    
    return '9'; // Fallback
  }

  /**
   * 🏆 Find wins in grid
   */
  private findWins(grid: string[][]): any[] {
    const wins: any[] = [];
    
    // Check horizontal lines (simple 3+ in a row)
    for (let row = 0; row < 4; row++) {
      let currentSymbol = grid[0][row];
      let count = 1;
      let positions = [{ reel: 0, row }];
      
      for (let reel = 1; reel < 5; reel++) {
        if (grid[reel][row] === currentSymbol && currentSymbol !== 'SCATTER') {
          count++;
          positions.push({ reel, row });
        } else {
          if (count >= 3) {
            wins.push({
              symbol: currentSymbol,
              count,
              positions: [...positions],
              line: row,
              payout: this.calculateLinePayout(currentSymbol, count)
            });
          }
          currentSymbol = grid[reel][row];
          count = 1;
          positions = [{ reel, row }];
        }
      }
      
      // Check final sequence
      if (count >= 3) {
        wins.push({
          symbol: currentSymbol,
          count,
          positions,
          line: row,
          payout: this.calculateLinePayout(currentSymbol, count)
        });
      }
    }

    // Check for scatters (3+ anywhere)
    const scatterPositions: any[] = [];
    for (let reel = 0; reel < 5; reel++) {
      for (let row = 0; row < 4; row++) {
        if (grid[reel][row] === 'SCATTER') {
          scatterPositions.push({ reel, row });
        }
      }
    }
    
    if (scatterPositions.length >= 3) {
      wins.push({
        symbol: 'SCATTER',
        count: scatterPositions.length,
        positions: scatterPositions,
        line: -1, // Special indicator for scatter
        payout: this.calculateScatterPayout(scatterPositions.length)
      });
    }

    return wins;
  }

  /**
   * 💰 Calculate line payout
   */
  private calculateLinePayout(symbol: string, count: number): number {
    const payouts: Record<string, number[]> = {
      '9': [0, 0, 5, 15, 50],
      '10': [0, 0, 5, 20, 60],
      'J': [0, 0, 10, 25, 75],
      'Q': [0, 0, 10, 30, 80],
      'K': [0, 0, 15, 40, 100],
      'A': [0, 0, 20, 50, 150],
      'WILD': [0, 0, 50, 200, 500],
      'SCATTER': [0, 0, 10, 50, 200]
    };

    return payouts[symbol]?.[count] || 0;
  }

  /**
   * ⭐ Calculate scatter payout
   */
  private calculateScatterPayout(count: number): number {
    const scatterPayouts = [0, 0, 0, 50, 200, 1000]; // 3, 4, 5 scatters
    return scatterPayouts[count] || 0;
  }

  /**
   * 💰 Calculate total win amount
   */
  private calculateWinAmount(wins: any[]): number {
    return wins.reduce((total, win) => total + (win.payout || 0), 0);
  }

  /**
   * 🎬 Animate cascade
   */
  private async animateCascade(cascadeNumber: number): Promise<void> {
    return new Promise(resolve => {
      console.log(`🎬 Animating cascade ${cascadeNumber}`);
      
      // Add cascade animation classes
      const reels = document.querySelectorAll('.reel-column');
      reels.forEach((reel, index) => {
        setTimeout(() => {
          reel.classList.add('cascade-drop');
          setTimeout(() => {
            reel.classList.remove('cascade-drop');
          }, 600);
        }, index * 100);
      });

      // Resolve after animation completes
      setTimeout(resolve, 1000);
    });
  }

  /**
   * 🎯 Animate winning symbols removal
   */
  public async animateWinRemoval(wins: any[]): Promise<void> {
    return new Promise(resolve => {
      console.log(`✨ Animating win removal for ${wins.length} wins`);
      
      // Highlight winning symbols
      wins.forEach(win => {
        win.positions?.forEach((pos: any) => {
          const symbolElement = document.querySelector(
            `.reel-column:nth-child(${pos.reel + 1}) .symbol-container:nth-child(${pos.row + 1})`
          );
          if (symbolElement) {
            symbolElement.classList.add('winning-symbol', 'cascade-remove');
          }
        });
      });

      // Remove after animation
      setTimeout(() => {
        wins.forEach(win => {
          win.positions?.forEach((pos: any) => {
            const symbolElement = document.querySelector(
              `.reel-column:nth-child(${pos.reel + 1}) .symbol-container:nth-child(${pos.row + 1})`
            );
            if (symbolElement) {
              symbolElement.classList.remove('winning-symbol', 'cascade-remove');
            }
          });
        });
        resolve();
      }, 800);
    });
  }
}

export const cascadeManager = new CascadeManager();
