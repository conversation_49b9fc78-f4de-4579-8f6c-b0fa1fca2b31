import {
  users,
  gameSessions,
  spinResults,
  depositRequests,
  withdrawalRequests,
  type User,
  type InsertUser,
  type GameSession,
  type InsertGameSession,
  type SpinResult,
  type InsertSpinResult,
  type DepositRequest,
  type InsertDepositRequest,
  type WithdrawalRequest,
  type InsertWithdrawalRequest
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, sum, count } from "drizzle-orm";
import session from "express-session";
import createMemoryStore from "memorystore";
import { randomBytes, createHash } from "crypto";
import { SlotPsychologyEngine } from "./psychology-engine";
import { EnhancedPsychologyEngine } from "./enhanced-psychology-engine";
import { superAceWinCalculator } from "./super-ace-win-calculator";
// import { consecutiveWinMultiplier } from "./consecutive-win-multiplier";
import { simpleGameEngine } from "./simple-game-engine";
import { advancedAISlotEngine } from "./advanced-ai-slot-engine";
// import { superAceReelEngine } from "./super-ace-reel-engine";

const MemoryStore = createMemoryStore(session);

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByMobile(mobile: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUserLastLogin(userId: number): Promise<void>;
  updateUserBalance(userId: number, newBalance: string): Promise<void>;
  getUserStats(userId: number): Promise<any>;

  createGameSession(session: InsertGameSession): Promise<GameSession>;
  getGameSession(sessionId: string): Promise<GameSession | undefined>;
  updateSessionBalance(sessionId: string, balance: string): Promise<void>;
  getSessionStats(sessionId: string): Promise<any>;

  processSpin(params: {
    sessionId: string;
    userId: number;
    betAmount: string;
    isFreeSpinRound: boolean;
    session: GameSession;
  }): Promise<any>;

  // Admin methods
  getAllUsers(): Promise<User[]>;
  toggleUserStatus(userId: number, isActive: boolean): Promise<void>;

  // Deposit methods
  createDepositRequest(request: InsertDepositRequest): Promise<DepositRequest>;
  getDepositRequests(status?: string): Promise<DepositRequest[]>;
  processDepositRequest(requestId: number, status: 'approved' | 'rejected', adminId: number, notes?: string): Promise<void>;

  // Withdrawal methods
  createWithdrawalRequest(request: InsertWithdrawalRequest): Promise<WithdrawalRequest>;
  getWithdrawalRequests(status?: string): Promise<WithdrawalRequest[]>;
  processWithdrawalRequest(requestId: number, status: 'completed' | 'rejected', adminId: number, notes?: string): Promise<void>;

  // Admin dashboard stats
  getAdminStats(): Promise<any>;

  // 🎁 Daily bonuses and engagement
  checkDailyLogin(userId: number): Promise<{ hasBonus: boolean; amount?: number; streak?: number }>;
  claimDailyBonus(userId: number): Promise<{ amount: number; newStreak: number }>;
  checkFreeSpins(userId: number): Promise<{ hasFreeSpin: boolean; remaining?: number }>;
  claimFreeSpin(userId: number): Promise<{ success: boolean; remaining: number }>;

  sessionStore: any;
}

// Crypto-secure RNG implementation
class CryptoRNG {
  private seed: string;
  private counter: number;

  constructor(seed: string) {
    this.seed = seed;
    this.counter = 0;
  }

  next(): number {
    this.counter++;
    const input = this.seed + this.counter.toString();
    const hash = createHash('sha256').update(input).digest();

    // Convert first 4 bytes to number and normalize to [0, 1)
    const value = hash.readUInt32BE(0) / 0xffffffff;
    return value;
  }

  nextInt(min: number, max: number): number {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }

  reseed() {
    this.seed = randomBytes(32).toString('hex');
    this.counter = 0;
  }
}

// Symbol definitions with realistic casino weights
const symbols = {
  '9': { weight: 35, payout: [0, 0, 2, 10, 50], rarity: 'common' },
  '10': { weight: 30, payout: [0, 0, 3, 15, 75], rarity: 'common' },
  'J': { weight: 25, payout: [0, 0, 4, 20, 100], rarity: 'uncommon' },
  'Q': { weight: 20, payout: [0, 0, 5, 25, 125], rarity: 'uncommon' },
  'K': { weight: 15, payout: [0, 0, 6, 30, 150], rarity: 'rare' },
  'A': { weight: 12, payout: [0, 0, 8, 40, 200], rarity: 'rare' },
  'SCATTER': { weight: 3, payout: [0, 0, 2, 8, 75], rarity: 'scatter' },
  'WILD': { weight: 2, payout: [0, 0, 20, 75, 300], rarity: 'wild' }
};

export class DatabaseStorage implements IStorage {
  sessionStore: any;
  private enhancedPsychology: EnhancedPsychologyEngine;

  constructor() {
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000, // 24 hours
    });
    this.enhancedPsychology = new EnhancedPsychologyEngine();
  }

  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByMobile(mobile: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.mobile, mobile));
    return user || undefined;
  }



  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async updateUserLastLogin(userId: number): Promise<void> {
    await db
      .update(users)
      .set({ lastLogin: new Date() })
      .where(eq(users.id, userId));
  }

  async updateUserBalance(userId: number, newBalance: string): Promise<void> {
    await db
      .update(users)
      .set({ balance: newBalance })
      .where(eq(users.id, userId));
  }

  async getUserStats(userId: number): Promise<any> {
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    const sessionStats = await db
      .select({
        totalSessions: count(),
        totalBet: sum(gameSessions.totalBet),
        totalWon: sum(gameSessions.totalWon),
      })
      .from(gameSessions)
      .where(eq(gameSessions.userId, userId));

    return {
      user,
      sessions: sessionStats[0] || { totalSessions: 0, totalBet: "0", totalWon: "0" }
    };
  }

  async createGameSession(session: InsertGameSession): Promise<GameSession> {
    const [newSession] = await db
      .insert(gameSessions)
      .values(session)
      .returning();
    return newSession;
  }

  async getGameSession(sessionId: string): Promise<GameSession | undefined> {
    const [session] = await db
      .select()
      .from(gameSessions)
      .where(eq(gameSessions.sessionId, sessionId));
    return session || undefined;
  }

  async updateSessionBalance(sessionId: string, balance: string): Promise<void> {
    await db
      .update(gameSessions)
      .set({
        currentBalance: balance,
        updatedAt: new Date()
      })
      .where(eq(gameSessions.sessionId, sessionId));
  }

  async getSessionStats(sessionId: string): Promise<any> {
    const spinStats = await db
      .select({
        totalSpins: count(),
        totalBet: sum(spinResults.betAmount),
        totalWon: sum(spinResults.winAmount),
      })
      .from(spinResults)
      .where(eq(spinResults.sessionId, sessionId));

    return spinStats[0] || { totalSpins: 0, totalBet: "0", totalWon: "0" };
  }

  // 🧠 Enhanced spin processing with natural game feel and psychology
  async processSpin(params: {
    sessionId: string;
    userId: number;
    betAmount: string;
    isFreeSpinRound: boolean;
    session: GameSession;
  }): Promise<any> {
    const { sessionId, userId, betAmount, isFreeSpinRound, session } = params;
    const bet = parseFloat(betAmount);

    console.log(`🎰 Super Ace Spin Processing: User ${userId}, Bet $${bet}`);

    // Get user data for psychology calculations
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    // 🚀 Initialize session for adaptive loss tracking (NEW FEATURE)
    advancedAISlotEngine.initializeSession(userId, parseFloat(session.currentBalance));

    // 🎯 TEMPORARY: Use enhanced psychology while fixing deposit balance engine
    const dynamicWinChance = this.enhancedPsychology.calculateDynamicWinChance(user, bet);
    const bonusChance = this.enhancedPsychology.calculateBonusChance(user, bet);

    // Create temporary luck adjustment
    const luckAdjustment = {
      shouldWin: false,
      winType: 'small' as const,
      winChance: dynamicWinChance,
      maxWinMultiplier: 3.0,
      scatterChance: bonusChance,
      reason: 'enhanced_psychology_fallback',
      hookStrategy: 'normal' as const
    };

    // Initialize RNG with session seed
    const rng = new CryptoRNG(session.rngSeed);

    // 🎲 Determine if this spin should win based on deposit balance
    const shouldWin = luckAdjustment.shouldWin || rng.next() < luckAdjustment.winChance;
    const shouldBonus = rng.next() < luckAdjustment.scatterChance;
    const shouldNearMiss = !shouldWin && rng.next() < 0.1; // 10% near miss chance

    // 🧠 Advanced AI-powered game decision with psychology
    const advancedDecision = advancedAISlotEngine.makeAdvancedGameDecision(userId, bet);

    // 🎰 SUPER ACE STYLE REEL GENERATION
    // Calculate player performance for dynamic card distribution
    const playerPerformance = {
      totalBet: parseFloat(session.totalBet),
      totalWon: parseFloat(session.totalWon),
      winRate: parseFloat(session.totalBet) > 0 ? parseFloat(session.totalWon) / parseFloat(session.totalBet) : 0,
      lossStreak: user.lossStreak || 0,
      winStreak: user.winStreak || 0,
      recentSpins: [] // We'll implement this later
    };

    console.log(`🎰 Super Ace Player Performance: Win Rate: ${(playerPerformance.winRate * 100).toFixed(1)}%, Loss Streak: ${playerPerformance.lossStreak}, Win Streak: ${playerPerformance.winStreak}`);

    // Generate authentic Super Ace style reels with dynamic card distribution
    const grid = this.generateSuperAceStyleReels(playerPerformance, advancedDecision, bet, rng);

    console.log(`🧠 Advanced AI decision for user ${userId}:`, advancedDecision);

    // Calculate wins using Super Ace calculator
    const winResult = superAceWinCalculator.calculateWins(grid, bet, 1);
    let baseWinAmount = winResult.totalPayout;

    // Apply advanced AI decision multiplier and psychology
    if (advancedDecision.shouldWin && baseWinAmount > 0) {
      baseWinAmount *= advancedDecision.winMultiplier;
    }

    // 🎯 SIMPLE MULTIPLIER SYSTEM (Temporary - will implement consecutive wins later)
    let totalWin = baseWinAmount;
    const newConsecutiveWins = 0; // Temporary
    const newMultiplier = session.currentMultiplier || 1; // Keep current multiplier

    console.log(`🎰 Super Ace Win: Base: $${baseWinAmount.toFixed(2)}, Final: $${totalWin.toFixed(2)}`);

    // Handle Loss-Disguised-as-Win (LDW) psychology
    if (advancedDecision.outcomeType === 'ldw' && totalWin < bet) {
      console.log(`🎭 LDW Applied: Win ${totalWin.toFixed(2)} < Bet ${bet.toFixed(2)} (net loss but celebrated)`);
    }

    // Update advanced AI player state
    advancedAISlotEngine.updatePlayerState(userId, totalWin > 0, totalWin, advancedDecision.outcomeType);

    // Handle bonus triggers
    let scatterTriggered = winResult.hasBonus;
    let bonusTriggered = winResult.hasBonus;

    // Deduct bet (unless free spin)
    let newBalance = parseFloat(session.currentBalance);
    console.log(`💰 BALANCE DEBUG: Starting balance: ${newBalance}, Bet: ${bet}, Free spin: ${isFreeSpinRound}`);

    if (!isFreeSpinRound) {
      newBalance -= bet;
      console.log(`💰 BALANCE DEBUG: After bet deduction: ${newBalance}`);
    }

    // Add winnings
    newBalance += totalWin;
    console.log(`💰 BALANCE DEBUG: After adding winnings (${totalWin}): ${newBalance}`);

    // 🚀 Update session balance for adaptive loss tracking (NEW FEATURE)
    advancedAISlotEngine.updateSessionBalance(userId, isFreeSpinRound ? 0 : bet, totalWin);

    // 🎯 TEMPORARY: Disabled deposit balance engine update
    // depositBalanceEngine.updateAfterSpin(userId, totalWin > 0, totalWin, bet);

    // 🧠 Update user psychology after spin
    const psychologyUpdates = this.enhancedPsychology.updateUserPsychology(
      user,
      totalWin > 0,
      totalWin,
      bet
    );

    // Update user with psychology data
    if (Object.keys(psychologyUpdates).length > 0) {
      await db
        .update(users)
        .set(psychologyUpdates)
        .where(eq(users.id, userId));
    }

    // Update session
    await db
      .update(gameSessions)
      .set({
        currentBalance: newBalance.toFixed(2),
        totalBet: isFreeSpinRound ? session.totalBet : (parseFloat(session.totalBet) + bet).toFixed(2),
        totalWon: (parseFloat(session.totalWon) + totalWin).toFixed(2),
        spinsCount: session.spinsCount + 1,
        currentMultiplier: newMultiplier,
        freeSpinsRemaining: bonusTriggered ? 10 : Math.max(0, session.freeSpinsRemaining - (isFreeSpinRound ? 1 : 0)),
        updatedAt: new Date()
      })
      .where(eq(gameSessions.sessionId, sessionId));

    // Update user balance and total spins
    console.log(`💰 BALANCE DEBUG: Updating user ${userId} balance from ${user.balance} to ${newBalance.toFixed(2)}`);
    await db
      .update(users)
      .set({
        balance: newBalance.toFixed(2),
        totalSpins: user.totalSpins + 1,
        totalWins: totalWin > 0 ? (parseFloat(user.totalWins) + totalWin).toFixed(2) : user.totalWins
      })
      .where(eq(users.id, userId));

    console.log(`💰 BALANCE DEBUG: User balance update completed`);

    // Record spin result
    const spinResult = await db
      .insert(spinResults)
      .values({
        sessionId,
        userId,
        betAmount,
        winAmount: totalWin.toFixed(2),
        symbols: grid,
        winLines: winResult.wins,
        multiplier: newMultiplier,
        isFreeSpinRound,
        scatterTriggered,
        bonusTriggered,
        rngValues: [], // We'll add this if needed
      })
      .returning();

    return {
      grid,
      wins: winResult.wins,
      totalWin: totalWin.toFixed(2),
      baseWin: baseWinAmount.toFixed(2), // 🎯 Show base win before multiplier
      newBalance: newBalance.toFixed(2),
      multiplier: newMultiplier,
      scatterTriggered,
      bonusTriggered,
      freeSpinsRemaining: bonusTriggered ? 10 : Math.max(0, session.freeSpinsRemaining - (isFreeSpinRound ? 1 : 0)),
      spinId: spinResult[0].id,
      // 🧠 Advanced AI Decision Data
      aiDecision: {
        reason: advancedDecision.reason,
        shouldWin: advancedDecision.shouldWin,
        winMultiplier: advancedDecision.winMultiplier,
        outcomeType: advancedDecision.outcomeType,
        psychologicalEffect: advancedDecision.psychologicalEffect,
        bonusForced: advancedDecision.bonusForced
      },
      // 🎰 Super Ace Style Data
      superAceStyle: {
        playerPerformance: playerPerformance,
        cardDistribution: "Dynamic based on player performance",
        boostApplied: playerPerformance.winRate < 0.3 || playerPerformance.lossStreak >= 5,
        reductionApplied: playerPerformance.winRate > 0.7 || playerPerformance.winStreak >= 8
      },
      // 📊 Advanced User Stats
      userStats: advancedAISlotEngine.getPlayerStats(userId),
      // 🌊 Cascade Data (legacy compatibility)
      cascadeCount: 0,
      // 🎯 Big win celebration trigger
      isBigWin: totalWin >= bet * 5,
      isMegaWin: totalWin >= bet * 10,
      // 🎰 Enhanced data for pattern selection
      winAmount: totalWin,
      winLines: winResult.wins,
      scatterCount: scatterTriggered ? 3 : 0,
      hasBonus: bonusTriggered,
      freeSpinsTriggered: bonusTriggered ? 10 : 0,
    };
  }

  // 👤 Get user profile for reel selection
  private async getUserProfileForReels(userId: number): Promise<any> {
    const user = await this.getUser(userId);
    if (!user) {
      return {
        userLevel: 'new',
        retentionRisk: 'low',
        consecutiveLosses: 0
      };
    }

    // Determine user level based on total deposits
    let userLevel: 'new' | 'retained' | 'vip' | 'whale' = 'new';
    const totalDeposits = parseFloat(user.totalDeposits);

    if (totalDeposits >= 10000) {
      userLevel = 'whale';
    } else if (totalDeposits >= 2000) {
      userLevel = 'vip';
    } else if (totalDeposits > 0 || user.totalSpins > 10) {
      userLevel = 'retained';
    }

    // Determine retention risk based on recent activity
    let retentionRisk: 'low' | 'medium' | 'high' = 'low';
    const lossStreak = user.lossStreak || 0;

    if (lossStreak >= 12) {
      retentionRisk = 'high';
    } else if (lossStreak >= 6) {
      retentionRisk = 'medium';
    }

    return {
      userLevel,
      retentionRisk,
      consecutiveLosses: lossStreak
    };
  }

  // Super Ace Style 1024 ways to win calculation
  private calculateWins(grid: string[][], betAmount: number): any {
    // Use the new Super Ace win calculator
    const result = superAceWinCalculator.calculateWins(grid, betAmount, 1);
    return result.wins; // Return just the wins array for compatibility
  }

  private findWinPath(grid: string[][], targetSymbol: string, reel: number, row: number): any[] {
    const path = [{ reel, row }];

    for (let nextReel = reel + 1; nextReel < 5; nextReel++) {
      let foundMatch = false;

      for (let nextRow = 0; nextRow < 4; nextRow++) {
        const symbol = grid[nextReel][nextRow];
        if (symbol === targetSymbol || symbol === 'WILD' || targetSymbol === 'WILD') {
          path.push({ reel: nextReel, row: nextRow });
          foundMatch = true;
          break;
        }
      }

      if (!foundMatch) break;
    }

    return path;
  }

  private findScatters(grid: string[][]): any[] {
    const scatters: any[] = [];
    for (let reel = 0; reel < 5; reel++) {
      for (let row = 0; row < 4; row++) {
        if (grid[reel][row] === 'SCATTER') {
          scatters.push({ reel, row });
        }
      }
    }
    return scatters;
  }

  // 🎰 Generate winning grid with guaranteed wins
  private generateWinningGrid(rng: CryptoRNG, shouldBonus: boolean): string[][] {
    const grid: string[][] = [];
    const symbolKeys = Object.keys(symbols).filter(s => s !== 'SCATTER' && s !== 'WILD');

    // Pick a winning symbol
    const winSymbol = symbolKeys[Math.floor(rng.next() * symbolKeys.length)];

    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      for (let row = 0; row < 4; row++) {
        if (reel < 3 && row === 0) {
          // Guarantee winning line on top row for first 3 reels
          grid[reel][row] = winSymbol;
        } else if (shouldBonus && reel < 3 && row === 1) {
          // Add scatters for bonus
          grid[reel][row] = 'SCATTER';
        } else {
          // Fill with random symbols
          const allSymbols = Object.keys(symbols);
          grid[reel][row] = allSymbols[Math.floor(rng.next() * allSymbols.length)];
        }
      }
    }

    return grid;
  }

  // 🎯 Generate near-miss grid (psychological hook)
  private generateNearMissGrid(rng: CryptoRNG): string[][] {
    const grid: string[][] = [];

    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      for (let row = 0; row < 4; row++) {
        if (reel < 2 && row === 1) {
          // Place 2 scatters to create "almost bonus" feeling
          grid[reel][row] = 'SCATTER';
        } else if (reel === 2 && row === 1) {
          // Third scatter just misses
          grid[reel][row] = Math.random() < 0.5 ? 'WILD' : 'A';
        } else {
          // Fill with random symbols
          const allSymbols = Object.keys(symbols);
          grid[reel][row] = allSymbols[Math.floor(rng.next() * allSymbols.length)];
        }
      }
    }

    return grid;
  }

  // 💸 Generate losing grid
  private generateLosingGrid(rng: CryptoRNG): string[][] {
    const grid: string[][] = [];
    const symbolKeys = Object.keys(symbols);

    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      for (let row = 0; row < 4; row++) {
        // Ensure no winning combinations
        let symbol;
        do {
          symbol = symbolKeys[Math.floor(rng.next() * symbolKeys.length)];
        } while (this.wouldCreateWin(grid, reel, row, symbol));

        grid[reel][row] = symbol;
      }
    }

    return grid;
  }

  // Helper to check if placing a symbol would create a win
  private wouldCreateWin(grid: string[][], reel: number, row: number, symbol: string): boolean {
    if (reel < 2) return false; // First two reels can't create wins yet

    // Check if this would complete a 3+ symbol line
    if (reel >= 2) {
      for (let checkRow = 0; checkRow < 4; checkRow++) {
        if (grid[0] && grid[1] &&
            grid[0][checkRow] === symbol &&
            grid[1][checkRow] === symbol) {
          return true; // Would create a 3-symbol win
        }
      }
    }

    return false;
  }

  // Admin methods implementation
  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users).orderBy(desc(users.joinDate));
  }

  async toggleUserStatus(userId: number, isActive: boolean): Promise<void> {
    await db
      .update(users)
      .set({ isActive })
      .where(eq(users.id, userId));
  }

  // Deposit methods
  async createDepositRequest(request: InsertDepositRequest): Promise<DepositRequest> {
    const [depositRequest] = await db
      .insert(depositRequests)
      .values(request)
      .returning();
    return depositRequest;
  }

  async getDepositRequests(status?: string): Promise<DepositRequest[]> {
    if (status) {
      return await db
        .select()
        .from(depositRequests)
        .where(eq(depositRequests.status, status))
        .orderBy(desc(depositRequests.createdAt));
    }
    return await db
      .select()
      .from(depositRequests)
      .orderBy(desc(depositRequests.createdAt));
  }

  async processDepositRequest(requestId: number, status: 'approved' | 'rejected', adminId: number, notes?: string): Promise<void> {
    const [request] = await db
      .select()
      .from(depositRequests)
      .where(eq(depositRequests.id, requestId));

    if (!request) throw new Error("Deposit request not found");

    // Update request status
    await db
      .update(depositRequests)
      .set({
        status,
        adminNotes: notes,
        processedBy: adminId,
        processedAt: new Date()
      })
      .where(eq(depositRequests.id, requestId));

    // If approved, credit user balance
    if (status === 'approved') {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, request.userId));

      if (user) {
        const newBalance = (parseFloat(user.balance) + parseFloat(request.amount)).toFixed(2);
        await this.updateUserBalance(request.userId, newBalance);

        // Update total deposits
        const newTotalDeposits = (parseFloat(user.totalDeposits) + parseFloat(request.amount)).toFixed(2);
        await db
          .update(users)
          .set({ totalDeposits: newTotalDeposits })
          .where(eq(users.id, request.userId));

        // 🎯 TEMPORARY: Disabled deposit balance engine refresh
        // await depositBalanceEngine.refreshUserBalance(request.userId);
      }
    }
  }

  // Withdrawal methods
  async createWithdrawalRequest(request: InsertWithdrawalRequest): Promise<WithdrawalRequest> {
    // First, deduct the amount from user balance
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, request.userId));

    if (!user) throw new Error("User not found");

    const currentBalance = parseFloat(user.balance);
    const withdrawAmount = parseFloat(request.amount.toString());

    if (currentBalance < withdrawAmount) {
      throw new Error("Insufficient balance");
    }

    // Deduct amount from balance
    const newBalance = (currentBalance - withdrawAmount).toFixed(2);
    await this.updateUserBalance(request.userId, newBalance);

    // Create withdrawal request
    const [withdrawalRequest] = await db
      .insert(withdrawalRequests)
      .values(request)
      .returning();

    return withdrawalRequest;
  }

  async getWithdrawalRequests(status?: string): Promise<WithdrawalRequest[]> {
    if (status) {
      return await db
        .select()
        .from(withdrawalRequests)
        .where(eq(withdrawalRequests.status, status))
        .orderBy(desc(withdrawalRequests.createdAt));
    }
    return await db
      .select()
      .from(withdrawalRequests)
      .orderBy(desc(withdrawalRequests.createdAt));
  }

  async processWithdrawalRequest(requestId: number, status: 'completed' | 'rejected', adminId: number, notes?: string): Promise<void> {
    const [request] = await db
      .select()
      .from(withdrawalRequests)
      .where(eq(withdrawalRequests.id, requestId));

    if (!request) throw new Error("Withdrawal request not found");

    // Update request status
    await db
      .update(withdrawalRequests)
      .set({
        status,
        adminNotes: notes,
        processedBy: adminId,
        processedAt: new Date()
      })
      .where(eq(withdrawalRequests.id, requestId));

    // If rejected, return money to user balance
    if (status === 'rejected') {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, request.userId));

      if (user) {
        const newBalance = (parseFloat(user.balance) + parseFloat(request.amount)).toFixed(2);
        await this.updateUserBalance(request.userId, newBalance);
      }
    } else if (status === 'completed') {
      // Update total withdrawals
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, request.userId));

      if (user) {
        const newTotalWithdrawals = (parseFloat(user.totalWithdrawals) + parseFloat(request.amount)).toFixed(2);
        await db
          .update(users)
          .set({ totalWithdrawals: newTotalWithdrawals })
          .where(eq(users.id, request.userId));

        // 🎯 TEMPORARY: Disabled deposit balance engine refresh
        // await depositBalanceEngine.refreshUserBalance(request.userId);
      }
    }
  }

  async getAdminStats(): Promise<any> {
    const totalUsers = await db.select({ count: count() }).from(users);
    const activeUsers = await db.select({ count: count() }).from(users).where(eq(users.isActive, true));

    const pendingDeposits = await db
      .select({ count: count(), total: sum(depositRequests.amount) })
      .from(depositRequests)
      .where(eq(depositRequests.status, 'pending'));

    const pendingWithdrawals = await db
      .select({ count: count(), total: sum(withdrawalRequests.amount) })
      .from(withdrawalRequests)
      .where(eq(withdrawalRequests.status, 'pending'));

    const totalBalance = await db.select({ total: sum(users.balance) }).from(users);

    return {
      totalUsers: totalUsers[0]?.count || 0,
      activeUsers: activeUsers[0]?.count || 0,
      pendingDeposits: {
        count: pendingDeposits[0]?.count || 0,
        total: pendingDeposits[0]?.total || "0"
      },
      pendingWithdrawals: {
        count: pendingWithdrawals[0]?.count || 0,
        total: pendingWithdrawals[0]?.total || "0"
      },
      totalBalance: totalBalance[0]?.total || "0"
    };
  }

  // 🎁 Daily Login Bonus System
  async checkDailyLogin(userId: number): Promise<{ hasBonus: boolean; amount?: number; streak?: number }> {
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    const today = new Date().toISOString().split('T')[0];
    const lastLoginDate = user.lastLoginDate;

    if (lastLoginDate === today) {
      // Already claimed today
      return { hasBonus: false };
    }

    // Calculate new streak
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];

    let newStreak = 1;
    if (lastLoginDate === yesterdayStr) {
      // Consecutive day
      newStreak = (user.loginStreak || 0) + 1;
    }

    const bonusAmount = this.enhancedPsychology.calculateLoginBonus(newStreak);

    return {
      hasBonus: true,
      amount: bonusAmount,
      streak: newStreak
    };
  }

  async claimDailyBonus(userId: number): Promise<{ amount: number; newStreak: number }> {
    const bonusCheck = await this.checkDailyLogin(userId);
    if (!bonusCheck.hasBonus) {
      throw new Error("No daily bonus available");
    }

    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    const today = new Date().toISOString().split('T')[0];
    const bonusAmount = bonusCheck.amount!;
    const newStreak = bonusCheck.streak!;

    // Update user
    const newBalance = (parseFloat(user.balance) + bonusAmount).toFixed(2);

    await db
      .update(users)
      .set({
        balance: newBalance,
        loginStreak: newStreak,
        lastLoginDate: today
      })
      .where(eq(users.id, userId));

    return {
      amount: bonusAmount,
      newStreak
    };
  }

  // 🎰 Free Spins System (10 days for new users)
  async checkFreeSpins(userId: number): Promise<{ hasFreeSpin: boolean; remaining?: number }> {
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    return {
      hasFreeSpin: this.enhancedPsychology.shouldGetFreeSpinToday(user),
      remaining: user.freeSpinsRemaining || 0
    };
  }

  async claimFreeSpin(userId: number): Promise<{ success: boolean; remaining: number }> {
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    if (!this.enhancedPsychology.shouldGetFreeSpinToday(user)) {
      return { success: false, remaining: user.freeSpinsRemaining || 0 };
    }

    const today = new Date().toISOString().split('T')[0];
    const newRemaining = Math.max(0, (user.freeSpinsRemaining || 0) - 1);

    await db
      .update(users)
      .set({
        freeSpinsRemaining: newRemaining,
        lastFreeSpinDate: today
      })
      .where(eq(users.id, userId));

    return {
      success: true,
      remaining: newRemaining
    };
  }

  /**
   * 🎰 Generate Super Ace Style Reels with Dynamic Card Distribution
   * Cards drop in varied patterns and quality adjusts based on player performance
   */
  private generateSuperAceStyleReels(playerPerformance: any, advancedDecision: any, betAmount: number, rng: CryptoRNG): string[][] {
    console.log(`🎰 Generating Super Ace style reels...`);

    // 🎯 Card values (higher = better for player)
    const cardValues = {
      '9': 1, '10': 2, 'J': 3, 'Q': 4, 'K': 5, 'A': 6, 'WILD': 8, 'SCATTER': 10
    };

    // 🎰 Base card distribution (balanced)
    let distribution = {
      '9': 25,      // Most common, lowest value
      '10': 22,
      'J': 18,
      'Q': 15,
      'K': 12,
      'A': 8,
      'WILD': 5,    // Rare, high value
      'SCATTER': 3  // Rarest, highest value
    };

    // 📊 Analyze player performance and adjust card distribution
    const winRate = playerPerformance.winRate;
    const lossStreak = playerPerformance.lossStreak;
    const winStreak = playerPerformance.winStreak;

    // 🚀 BOOST SYSTEM: Player losing too much = better cards
    if (winRate < 0.3 || lossStreak >= 5) {
      const boostLevel = Math.min(5, Math.max(1,
        lossStreak >= 10 ? 5 :
        lossStreak >= 7 ? 4 :
        lossStreak >= 5 ? 3 :
        winRate < 0.2 ? 4 :
        winRate < 0.3 ? 2 : 1
      ));

      const boostMultiplier = 1 + (boostLevel * 0.3); // Up to 2.5x

      distribution['A'] = Math.floor(distribution['A'] * boostMultiplier);
      distribution['K'] = Math.floor(distribution['K'] * boostMultiplier);
      distribution['WILD'] = Math.floor(distribution['WILD'] * boostMultiplier);
      distribution['SCATTER'] = Math.floor(distribution['SCATTER'] * boostMultiplier);

      // Reduce low-value cards
      distribution['9'] = Math.floor(distribution['9'] * 0.7);
      distribution['10'] = Math.floor(distribution['10'] * 0.8);

      console.log(`🚀 BOOST APPLIED: Level ${boostLevel} - Player needs better cards (Win Rate: ${(winRate * 100).toFixed(1)}%, Loss Streak: ${lossStreak})`);
    }

    // ⬇️ REDUCTION SYSTEM: Player winning too much = worse cards
    else if (winRate > 0.7 || winStreak >= 8) {
      const reductionLevel = Math.min(5, Math.max(1,
        winStreak >= 15 ? 5 :
        winStreak >= 10 ? 4 :
        winStreak >= 8 ? 3 :
        winRate > 0.8 ? 4 :
        winRate > 0.7 ? 2 : 1
      ));

      const reductionMultiplier = 1 + (reductionLevel * 0.4); // Up to 3x

      distribution['9'] = Math.floor(distribution['9'] * reductionMultiplier);
      distribution['10'] = Math.floor(distribution['10'] * reductionMultiplier);
      distribution['J'] = Math.floor(distribution['J'] * reductionMultiplier);

      // Reduce high-value cards
      distribution['A'] = Math.floor(distribution['A'] * 0.5);
      distribution['WILD'] = Math.floor(distribution['WILD'] * 0.3);
      distribution['SCATTER'] = Math.floor(distribution['SCATTER'] * 0.2);

      console.log(`⬇️ REDUCTION APPLIED: Level ${reductionLevel} - Player winning too much (Win Rate: ${(winRate * 100).toFixed(1)}%, Win Streak: ${winStreak})`);
    }

    // 💰 Bet amount adjustments (higher bets = slightly better cards)
    if (betAmount >= 10) {
      distribution['A'] = Math.floor(distribution['A'] * 1.2);
      distribution['K'] = Math.floor(distribution['K'] * 1.1);
      console.log(`💰 High bet bonus applied for $${betAmount} bet`);
    }

    // 🎰 Generate reels with weighted symbol pool
    const symbolPool: string[] = [];
    for (const [symbol, weight] of Object.entries(distribution)) {
      for (let i = 0; i < weight; i++) {
        symbolPool.push(symbol);
      }
    }

    // Generate 5 reels with 4 rows each (Super Ace style)
    const reels: string[][] = [];
    for (let reel = 0; reel < 5; reel++) {
      reels[reel] = [];

      for (let row = 0; row < 4; row++) {
        // Authentic random selection from weighted pool
        const randomIndex = Math.floor(rng.next() * symbolPool.length);
        reels[reel][row] = symbolPool[randomIndex];
      }

      // Ensure variety - no reel should have all same symbols
      const uniqueSymbols = new Set(reels[reel]);
      if (uniqueSymbols.size < 2) {
        const availableSymbols = Array.from(new Set(symbolPool));
        const currentSymbol = reels[reel][0];
        const differentSymbols = availableSymbols.filter(s => s !== currentSymbol);
        const newSymbol = differentSymbols[Math.floor(rng.next() * differentSymbols.length)];
        const positionToReplace = Math.floor(rng.next() * 4);
        reels[reel][positionToReplace] = newSymbol;
      }
    }

    // 🎯 Apply final outcome adjustments
    if (advancedDecision.shouldWin) {
      this.ensureSuperAceWin(reels, advancedDecision.outcomeType, rng);
    } else {
      this.ensureSuperAceLoss(reels, rng);
    }

    console.log(`🎰 Generated Super Ace reels with ${advancedDecision.shouldWin ? 'WIN' : 'LOSS'} outcome`);
    return reels;
  }

  /**
   * ✅ Ensure winning pattern exists (Super Ace style)
   */
  private ensureSuperAceWin(reels: string[][], winType: string, rng: CryptoRNG): void {
    const symbols = ['A', 'K', 'Q', 'J', '10', '9'];
    const winSymbol = symbols[Math.floor(rng.next() * symbols.length)];

    // Create winning line on a random row
    const winRow = Math.floor(rng.next() * 4);
    const winLength = winType === 'big_win' ? 5 : 3;

    for (let reel = 0; reel < winLength; reel++) {
      reels[reel][winRow] = winSymbol;
    }

    console.log(`✅ Created ${winType} win with ${winSymbol} on row ${winRow}, length ${winLength}`);
  }

  /**
   * ❌ Ensure losing pattern (no wins)
   */
  private ensureSuperAceLoss(reels: string[][], rng: CryptoRNG): void {
    // Check each row for potential wins and break them
    for (let row = 0; row < 4; row++) {
      let consecutiveCount = 1;
      let currentSymbol = reels[0][row];

      for (let reel = 1; reel < 5; reel++) {
        if (reels[reel][row] === currentSymbol || reels[reel][row] === 'WILD' || currentSymbol === 'WILD') {
          consecutiveCount++;
          if (consecutiveCount >= 3) {
            // Break the winning pattern
            const breakSymbols = ['9', '10', 'J', 'Q'];
            const breakSymbol = breakSymbols[Math.floor(rng.next() * breakSymbols.length)];
            reels[reel][row] = breakSymbol;
            consecutiveCount = 1;
            currentSymbol = breakSymbol;
          }
        } else {
          consecutiveCount = 1;
          currentSymbol = reels[reel][row];
        }
      }
    }

    console.log(`❌ Ensured losing pattern - no winning lines`);
  }
}

export const storage = new DatabaseStorage();
