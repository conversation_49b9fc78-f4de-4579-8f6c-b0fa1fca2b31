/**
 * Vegas Ace Slots - Main Game Initialization
 * 5x4 Grid, 1024 Ways to Win, Cascading Reels
 */

/**
 * Main Game Scene
 */
class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });

        // Game configuration
        this.config = {
            reels: 5,
            rows: 4,
            symbolSize: 80,
            symbolSpacing: 10,
            waysToWin: 1024,
            rtp: 97,
            maxWin: 1500,
            baseBet: 1.00,
            maxBet: 100.00,
            minBet: 0.10,
            spinDuration: 2000, // 2 seconds per spin
            reelStopDelay: 200  // 200ms between reel stops
        };

        // Game state
        this.gameState = {
            balance: 1000.00,
            currentBet: 1.00,
            isSpinning: false,
            inFreeSpins: false,
            freeSpinsRemaining: 0,
            currentMultiplier: 1,
            totalWin: 0,
            lastWin: 0,
            spinCount: 0,
            totalSpent: 0,
            isAutoPlay: false,
            autoPlaySpins: 0
        };

        // Game systems
        this.gameEngine = null;
        this.symbolsManager = null;
        this.bonusSystem = null;
        this.winCalculator = null;
        this.uiManager = null;
        this.audioManager = null;

        // Game grid
        this.symbolGrid = [];
        this.symbolSprites = [];
        this.isInitialized = false;
    }

    preload() {
        // Create symbol textures programmatically
        this.createSymbolTextures();

        // Load any additional assets
        this.load.image('background', 'data:image/svg+xml;base64,' + btoa(this.createBackgroundSVG()));
    }

    create() {
        // Initialize game systems
        this.gameEngine = new GameEngine(this, this.config, this.gameState);
        this.symbolsManager = new SymbolsManager(this, this.config);
        this.bonusSystem = new BonusSystem(this, this.config, this.gameState);
        this.winCalculator = new WinCalculator(this, this.config);
        this.uiManager = new UIManager(this, this.gameState);
        this.audioManager = new AudioManager(this);

        // Set up the game board
        this.setupGameBoard();

        // Initialize UI
        this.uiManager.initialize();

        // Start background music
        this.audioManager.playBGM();

        // Initial spin to populate the grid
        this.initialSpin();

        // Hide loading screen
        document.getElementById('loading-screen').classList.add('hidden');
    }

    /**
     * Create symbol textures programmatically
     */
    createSymbolTextures() {
        const symbols = ['9', '10', 'J', 'Q', 'K', 'A', 'SCATTER', 'GOLDEN_CARD', 'LITTLE_JOKER', 'BIG_JOKER'];
        const colors = {
            '9': '#8B4513',
            '10': '#CD853F',
            'J': '#4169E1',
            'Q': '#9932CC',
            'K': '#DC143C',
            'A': '#FFD700',
            'SCATTER': '#FFD700',
            'GOLDEN_CARD': '#FFA500',
            'LITTLE_JOKER': '#FF69B4',
            'BIG_JOKER': '#FF1493'
        };

        symbols.forEach(symbol => {
            const graphics = this.add.graphics();
            const size = this.config.symbolSize;

            // Background
            graphics.fillStyle(0x1a1a2e);
            graphics.fillRoundedRect(0, 0, size, size, 8);

            // Border
            graphics.lineStyle(2, Phaser.Display.Color.HexStringToColor(colors[symbol]).color);
            graphics.strokeRoundedRect(0, 0, size, size, 8);

            // Symbol text
            const text = this.add.text(size/2, size/2, symbol, {
                fontSize: symbol.length > 2 ? '12px' : '24px',
                fontFamily: 'Orbitron',
                color: colors[symbol],
                fontStyle: 'bold'
            }).setOrigin(0.5);

            // Generate texture
            graphics.generateTexture(symbol, size, size);
            text.destroy();
            graphics.destroy();
        });
    }

    /**
     * Create background SVG
     */
    createBackgroundSVG() {
        return `<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <radialGradient id="bg" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
                </radialGradient>
            </defs>
            <rect width="100%" height="100%" fill="url(#bg)"/>
        </svg>`;
    }

    /**
     * Set up the game board grid
     */
    setupGameBoard() {
        const startX = (this.cameras.main.width - (this.config.reels * (this.config.symbolSize + this.config.symbolSpacing))) / 2;
        const startY = (this.cameras.main.height - (this.config.rows * (this.config.symbolSize + this.config.symbolSpacing))) / 2;

        // Initialize grid arrays
        for (let reel = 0; reel < this.config.reels; reel++) {
            this.symbolGrid[reel] = [];
            this.symbolSprites[reel] = [];

            for (let row = 0; row < this.config.rows; row++) {
                this.symbolGrid[reel][row] = null;
                this.symbolSprites[reel][row] = null;
            }
        }

        // Create reel backgrounds
        for (let reel = 0; reel < this.config.reels; reel++) {
            const x = startX + reel * (this.config.symbolSize + this.config.symbolSpacing);
            const y = startY;
            const width = this.config.symbolSize;
            const height = this.config.rows * (this.config.symbolSize + this.config.symbolSpacing) - this.config.symbolSpacing;

            const reelBg = this.add.graphics();
            reelBg.fillStyle(0x000000, 0.3);
            reelBg.fillRoundedRect(x - 5, y - 5, width + 10, height + 10, 10);
            reelBg.lineStyle(2, 0xffd700, 0.5);
            reelBg.strokeRoundedRect(x - 5, y - 5, width + 10, height + 10, 10);
        }
    }

    /**
     * Initial spin to populate the grid
     */
    initialSpin() {
        for (let reel = 0; reel < this.config.reels; reel++) {
            for (let row = 0; row < this.config.rows; row++) {
                const symbol = this.symbolsManager.getRandomSymbol();
                this.symbolGrid[reel][row] = symbol;
                this.createSymbolSprite(reel, row, symbol);
            }
        }
    }

    /**
     * Create a symbol sprite at the specified position
     */
    createSymbolSprite(reel, row, symbol) {
        const startX = (this.cameras.main.width - (this.config.reels * (this.config.symbolSize + this.config.symbolSpacing))) / 2;
        const startY = (this.cameras.main.height - (this.config.rows * (this.config.symbolSize + this.config.symbolSpacing))) / 2;

        const x = startX + reel * (this.config.symbolSize + this.config.symbolSpacing);
        const y = startY + row * (this.config.symbolSize + this.config.symbolSpacing);

        if (this.symbolSprites[reel][row]) {
            this.symbolSprites[reel][row].destroy();
        }

        const sprite = this.add.image(x, y, symbol);
        sprite.setOrigin(0);
        sprite.setDisplaySize(this.config.symbolSize, this.config.symbolSize);

        this.symbolSprites[reel][row] = sprite;

        return sprite;
    }

    /**
     * Main spin function
     */
    async spin() {
        if (this.gameState.isSpinning) return;

        // Check if player has enough balance
        if (this.gameState.balance < this.gameState.currentBet && !this.gameState.inFreeSpins) {
            this.uiManager.showMessage('Insufficient balance!');
            return;
        }

        this.gameState.isSpinning = true;
        this.gameState.totalWin = 0;
        this.gameState.currentMultiplier = this.gameState.inFreeSpins ? 2 : 1;

        // Deduct bet (only if not in free spins)
        if (!this.gameState.inFreeSpins) {
            this.gameState.balance -= this.gameState.currentBet;
            this.uiManager.updateBalance();
        } else {
            this.gameState.freeSpinsRemaining--;
            this.uiManager.updateFreeSpins();
        }

        // Play spin start sound
        this.audioManager.play('spin_start');

        // Start reel spin sounds
        for (let reel = 0; reel < this.config.reels; reel++) {
            setTimeout(() => {
                this.audioManager.playReelSpin(reel);
            }, reel * 200); // Stagger reel sounds
        }

        // Start cascading sequence
        await this.cascadeSequence();

        // Stop all reel spin sounds
        this.audioManager.stopReelSpin();

        this.gameState.isSpinning = false;

        // Check if free spins ended
        if (this.gameState.inFreeSpins && this.gameState.freeSpinsRemaining <= 0) {
            this.gameState.inFreeSpins = false;
            this.uiManager.hideFreeSpinsDisplay();
            this.uiManager.showMessage('Free Spins Complete!');
        }
    }

    /**
     * Cascading sequence with win detection
     */
    async cascadeSequence() {
        let cascadeCount = 0;
        let hasWins = true;

        while (hasWins) {
            // Generate new symbols for empty positions
            await this.dropNewSymbols();

            // Check for wins
            const wins = this.winCalculator.calculateWins(this.symbolGrid);

            if (wins.length > 0) {
                cascadeCount++;

                // Update multiplier
                if (this.gameState.inFreeSpins) {
                    this.gameState.currentMultiplier = Math.min(10, 2 + (cascadeCount - 1) * 2);
                } else {
                    this.gameState.currentMultiplier = Math.min(5, 1 + (cascadeCount - 1));
                }

                this.uiManager.updateMultiplier();

                // Calculate win amount
                const winAmount = this.winCalculator.calculateWinAmount(wins, this.gameState.currentBet, this.gameState.currentMultiplier);
                this.gameState.totalWin += winAmount;
                this.gameState.balance += winAmount;

                // Highlight winning symbols
                await this.highlightWins(wins);

                // Remove winning symbols
                await this.removeWinningSymbols(wins);

                // Check for bonus triggers
                this.bonusSystem.checkBonusTriggers(this.symbolGrid, wins);

            } else {
                hasWins = false;
            }
        }

        // Update UI
        this.uiManager.updateBalance();
        this.uiManager.updateWin();

        if (this.gameState.totalWin > 0) {
            this.uiManager.showWinCelebration(this.gameState.totalWin);
        }

        // Hide multiplier if back to base
        if (this.gameState.currentMultiplier <= 1) {
            this.uiManager.hideMultiplierDisplay();
        }
    }

    /**
     * Drop new symbols from top
     */
    async dropNewSymbols() {
        const promises = [];

        for (let reel = 0; reel < this.config.reels; reel++) {
            for (let row = this.config.rows - 1; row >= 0; row--) {
                if (!this.symbolGrid[reel][row]) {
                    const symbol = this.symbolsManager.getRandomSymbol();
                    this.symbolGrid[reel][row] = symbol;

                    const startX = (this.cameras.main.width - (this.config.reels * (this.config.symbolSize + this.config.symbolSpacing))) / 2;
                    const startY = (this.cameras.main.height - (this.config.rows * (this.config.symbolSize + this.config.symbolSpacing))) / 2;

                    const x = startX + reel * (this.config.symbolSize + this.config.symbolSpacing);
                    const y = startY - this.config.symbolSize;

                    const sprite = this.add.image(x, y, symbol);
                    sprite.setOrigin(0);
                    sprite.setDisplaySize(this.config.symbolSize, this.config.symbolSize);

                    this.symbolSprites[reel][row] = sprite;

                    const targetY = startY + row * (this.config.symbolSize + this.config.symbolSpacing);

                    promises.push(
                        new Promise(resolve => {
                            this.tweens.add({
                                targets: sprite,
                                y: targetY,
                                duration: 300 + row * 50,
                                ease: 'Bounce.easeOut',
                                onComplete: resolve
                            });
                        })
                    );
                }
            }
        }

        await Promise.all(promises);
    }

    /**
     * Highlight winning symbols
     */
    async highlightWins(wins) {
        const promises = [];

        wins.forEach(win => {
            win.positions.forEach(pos => {
                const sprite = this.symbolSprites[pos.reel][pos.row];
                if (sprite) {
                    promises.push(
                        new Promise(resolve => {
                            this.tweens.add({
                                targets: sprite,
                                scaleX: 1.2,
                                scaleY: 1.2,
                                alpha: 0.8,
                                duration: 500,
                                yoyo: true,
                                onComplete: resolve
                            });
                        })
                    );
                }
            });
        });

        await Promise.all(promises);
    }

    /**
     * Remove winning symbols
     */
    async removeWinningSymbols(wins) {
        const promises = [];

        wins.forEach(win => {
            win.positions.forEach(pos => {
                const sprite = this.symbolSprites[pos.reel][pos.row];
                if (sprite) {
                    promises.push(
                        new Promise(resolve => {
                            this.tweens.add({
                                targets: sprite,
                                alpha: 0,
                                scaleX: 0,
                                scaleY: 0,
                                duration: 300,
                                onComplete: () => {
                                    sprite.destroy();
                                    this.symbolSprites[pos.reel][pos.row] = null;
                                    this.symbolGrid[pos.reel][pos.row] = null;
                                    resolve();
                                }
                            });
                        })
                    );
                }
            });
        });

        await Promise.all(promises);
    }

    /**
     * Handle reel stop
     * @param {number} reel - Reel index
     */
    handleReelStop(reel) {
        try {
            // Stop the specific reel's spin sound
            const sound = this.audioManager.sounds.reel_spin;
            if (sound && this.audioManager.currentlyPlaying.has('reel_spin')) {
                sound.setRate(1.0 - (reel * 0.1)); // Slow down the sound
            }

            // Play reel stop sound
            this.audioManager.play('reel_stop', {
                x: this.symbolSprites[reel][0].x,
                y: this.symbolSprites[reel][0].y,
                volume: 0.7 - (reel * 0.1) // Decrease volume for each reel
            });

            if (reel === this.config.reels - 1) {
                this.gameState.isSpinning = false;
                this.checkWins();
            }
        } catch (error) {
            console.error('Error during reel stop:', error);
        }
    }

    /**
     * Handle win celebration
     * @param {number} winAmount - Win amount
     */
    handleWinCelebration(winAmount) {
        try {
            this.gameState.lastWin = winAmount;
            
            // Play win sound sequence
            this.audioManager.playWinSound(winAmount, this.gameState.currentBet);
            
            // Show win celebration with animation
            this.uiManager.showWinCelebration(winAmount);

            // Update balance with animation
            this.uiManager.animateBalanceUpdate(this.gameState.balance);
        } catch (error) {
            console.error('Error during win celebration:', error);
        }
    }

    /**
     * Check for wins and handle payouts
     */
    async checkWins() {
        try {
            const wins = this.winCalculator.calculateWins(this.symbolGrid);
            
            if (wins.length > 0) {
                const winAmount = this.winCalculator.calculateWinAmount(wins, this.gameState.currentBet, this.gameState.currentMultiplier);
                this.gameState.totalWin += winAmount;
                
                // Handle win celebration
                this.handleWinCelebration(winAmount);
                
                // Check for bonus triggers
                await this.bonusSystem.checkBonusTriggers(this.symbolGrid, wins);
            }

            // Update UI
            this.uiManager.updateBalance();
            this.uiManager.updateWin();

            // Handle auto play
            if (this.gameState.isAutoPlay && this.gameState.autoPlaySpins > 0) {
                this.gameState.autoPlaySpins--;
                setTimeout(() => this.handleSpin(), 1000);
            }
        } catch (error) {
            console.error('Error checking wins:', error);
            this.gameState.isSpinning = false;
        }
    }
}

/**
 * Game Configuration
 */
const gameConfig = {
    type: Phaser.AUTO,
    width: 800,
    height: 600,
    parent: 'phaser-game',
    backgroundColor: '#1a1a2e',
    scene: GameScene,
    scale: {
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH,
        min: {
            width: 320,
            height: 240
        },
        max: {
            width: 1200,
            height: 900
        }
    },
    physics: {
        default: 'arcade',
        arcade: {
            debug: false
        }
    }
};

// Initialize the game
const game = new Phaser.Game(gameConfig);

// Export for debugging
window.game = game;
