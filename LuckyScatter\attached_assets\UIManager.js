/**
 * UIManager.js - User interface and controls management
 * Handles UI updates, animations, and user interactions
 */

class UIManager {
    /**
     * Initialize the UI manager
     * @param {Phaser.Scene} scene - The Phaser scene
     * @param {Object} gameState - Game state object
     */
    constructor(scene, gameState) {
        this.scene = scene;
        this.gameState = gameState;

        // UI elements
        this.elements = {
            balance: document.getElementById('balance'),
            betAmount: document.getElementById('bet-amount'),
            winAmount: document.getElementById('win-amount'),
            betDisplay: document.getElementById('bet-display'),
            spinBtn: document.getElementById('spin-btn'),
            multiplierDisplay: document.getElementById('multiplier-display'),
            multiplierValue: document.getElementById('multiplier-value'),
            freeSpinsDisplay: document.getElementById('freespins-display'),
            freeSpinsValue: document.getElementById('freespins-value'),
            winCelebration: document.getElementById('win-celebration'),
            winAmountBig: document.getElementById('win-amount-big')
        };

        // Animation queues
        this.animationQueue = [];
        this.isAnimating = false;

        // Message system
        this.messageQueue = [];
        this.currentMessage = null;
    }

    /**
     * Initialize UI manager
     */
    initialize() {
        this.updateAllDisplays();
        this.setupEventListeners();
        this.startAnimationLoop();
    }

    /**
     * Set up additional event listeners
     */
    setupEventListeners() {
        // Window resize handler
        window.addEventListener('resize', () => this.handleResize());

        // Visibility change handler
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
    }

    /**
     * Update all UI displays
     */
    updateAllDisplays() {
        this.updateBalance();
        this.updateBet();
        this.updateWin();
        this.updateSpinButton();
        this.updateMultiplier();
        this.updateFreeSpins();
    }

    /**
     * Update balance display
     */
    updateBalance() {
        if (this.elements.balance) {
            const formattedBalance = `$${this.gameState.balance.toFixed(2)}`;
            this.animateValueChange(this.elements.balance, formattedBalance);
        }
    }

    /**
     * Update bet display
     */
    updateBet() {
        const formattedBet = `$${this.gameState.currentBet.toFixed(2)}`;

        if (this.elements.betAmount) {
            this.elements.betAmount.textContent = formattedBet;
        }

        if (this.elements.betDisplay) {
            this.elements.betDisplay.textContent = formattedBet;
        }
    }

    /**
     * Update win display
     */
    updateWin() {
        if (this.elements.winAmount) {
            const formattedWin = `$${this.gameState.totalWin.toFixed(2)}`;
            this.animateValueChange(this.elements.winAmount, formattedWin);
        }
    }

    /**
     * Update spin button state
     */
    updateSpinButton() {
        if (!this.elements.spinBtn) return;

        const spinText = this.elements.spinBtn.querySelector('.spin-text');

        if (this.gameState.isSpinning) {
            this.elements.spinBtn.disabled = true;
            this.elements.spinBtn.classList.add('spinning');
            if (spinText) spinText.textContent = 'SPINNING...';
        } else if (this.gameState.inFreeSpins) {
            this.elements.spinBtn.disabled = false;
            this.elements.spinBtn.classList.remove('spinning');
            if (spinText) spinText.textContent = `FREE SPIN (${this.gameState.freeSpinsRemaining})`;
        } else {
            const canAfford = this.gameState.balance >= this.gameState.currentBet;
            this.elements.spinBtn.disabled = !canAfford;
            this.elements.spinBtn.classList.remove('spinning');
            if (spinText) spinText.textContent = 'SPIN';

            if (!canAfford) {
                this.elements.spinBtn.classList.add('insufficient-funds');
            } else {
                this.elements.spinBtn.classList.remove('insufficient-funds');
            }
        }
    }

    /**
     * Update multiplier display
     */
    updateMultiplier() {
        if (!this.elements.multiplierDisplay || !this.elements.multiplierValue) return;

        if (this.gameState.currentMultiplier > 1) {
            this.elements.multiplierValue.textContent = `x${this.gameState.currentMultiplier}`;
            this.elements.multiplierDisplay.classList.remove('hidden');

            // Add pulse animation for high multipliers
            if (this.gameState.currentMultiplier >= 5) {
                this.elements.multiplierDisplay.classList.add('high-multiplier');
            } else {
                this.elements.multiplierDisplay.classList.remove('high-multiplier');
            }
        } else {
            this.hideMultiplierDisplay();
        }
    }

    /**
     * Hide multiplier display
     */
    hideMultiplierDisplay() {
        if (this.elements.multiplierDisplay) {
            this.elements.multiplierDisplay.classList.add('hidden');
            this.elements.multiplierDisplay.classList.remove('high-multiplier');
        }
    }

    /**
     * Update free spins display
     */
    updateFreeSpins() {
        if (!this.elements.freeSpinsValue) return;

        if (this.gameState.inFreeSpins) {
            this.elements.freeSpinsValue.textContent = this.gameState.freeSpinsRemaining;
        }
    }

    /**
     * Show free spins display
     * @param {number} spinsCount - Number of free spins
     */
    showFreeSpinsDisplay(spinsCount) {
        if (this.elements.freeSpinsDisplay && this.elements.freeSpinsValue) {
            this.elements.freeSpinsValue.textContent = spinsCount;
            this.elements.freeSpinsDisplay.classList.remove('hidden');

            // Add entrance animation
            this.elements.freeSpinsDisplay.style.transform = 'scale(0)';
            this.elements.freeSpinsDisplay.style.opacity = '0';

            setTimeout(() => {
                this.elements.freeSpinsDisplay.style.transition = 'all 0.5s ease-out';
                this.elements.freeSpinsDisplay.style.transform = 'scale(1)';
                this.elements.freeSpinsDisplay.style.opacity = '1';
            }, 100);
        }
    }

    /**
     * Hide free spins display
     */
    hideFreeSpinsDisplay() {
        if (this.elements.freeSpinsDisplay) {
            this.elements.freeSpinsDisplay.style.transition = 'all 0.3s ease-in';
            this.elements.freeSpinsDisplay.style.transform = 'scale(0)';
            this.elements.freeSpinsDisplay.style.opacity = '0';

            setTimeout(() => {
                this.elements.freeSpinsDisplay.classList.add('hidden');
                this.elements.freeSpinsDisplay.style.transition = '';
                this.elements.freeSpinsDisplay.style.transform = '';
                this.elements.freeSpinsDisplay.style.opacity = '';
            }, 300);
        }
    }

    /**
     * Show win celebration
     * @param {number} winAmount - Win amount to display
     */
    showWinCelebration(winAmount) {
        if (!this.elements.winCelebration || !this.elements.winAmountBig) return;

        const winMultiplier = winAmount / this.gameState.currentBet;
        let celebrationType = 'win';

        if (winMultiplier >= 50) {
            celebrationType = 'mega';
        } else if (winMultiplier >= 25) {
            celebrationType = 'super';
        } else if (winMultiplier >= 10) {
            celebrationType = 'big';
        }

        // Update content
        this.elements.winAmountBig.textContent = `$${winAmount.toFixed(2)}`;
        const winText = this.elements.winCelebration.querySelector('.win-text');
        if (winText) {
            winText.textContent = `${celebrationType.toUpperCase()} WIN!`;
        }

        // Show celebration
        this.elements.winCelebration.classList.remove('hidden');
        this.elements.winCelebration.classList.add(celebrationType);

        // Auto-hide after animation
        setTimeout(() => {
            this.elements.winCelebration.classList.add('hidden');
            this.elements.winCelebration.classList.remove(celebrationType);
        }, 3000);
    }

    /**
     * Show message to player
     * @param {string} message - Message text
     * @param {number} duration - Display duration in ms
     */
    showMessage(message, duration = 2000) {
        this.messageQueue.push({ message, duration });
        this.processMessageQueue();
    }

    /**
     * Process message queue
     */
    processMessageQueue() {
        if (this.currentMessage || this.messageQueue.length === 0) return;

        const messageData = this.messageQueue.shift();
        this.currentMessage = this.createMessageElement(messageData.message);

        document.body.appendChild(this.currentMessage);

        // Auto-remove message
        setTimeout(() => {
            this.removeCurrentMessage();
        }, messageData.duration);
    }

    /**
     * Create message element
     * @param {string} message - Message text
     * @returns {HTMLElement} Message element
     */
    createMessageElement(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'game-message';
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #ffd700;
            padding: 20px 40px;
            border-radius: 10px;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 1.2rem;
            z-index: 1500;
            border: 2px solid #ffd700;
            animation: messageSlideIn 0.3s ease-out;
            text-align: center;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
        `;

        return messageDiv;
    }

    /**
     * Remove current message
     */
    removeCurrentMessage() {
        if (this.currentMessage) {
            this.currentMessage.style.animation = 'messageSlideOut 0.3s ease-in';

            setTimeout(() => {
                if (this.currentMessage && this.currentMessage.parentNode) {
                    this.currentMessage.parentNode.removeChild(this.currentMessage);
                }
                this.currentMessage = null;
                this.processMessageQueue();
            }, 300);
        }
    }

    /**
     * Animate value change
     * @param {HTMLElement} element - Element to animate
     * @param {string} newValue - New value to display
     */
    animateValueChange(element, newValue) {
        if (!element) return;

        const currentValue = element.textContent;
        if (currentValue === newValue) return;

        // Add animation class
        element.classList.add('value-changing');

        setTimeout(() => {
            element.textContent = newValue;
            element.classList.remove('value-changing');
        }, 150);
    }

    /**
     * Start animation loop
     */
    startAnimationLoop() {
        const animate = () => {
            this.processAnimationQueue();
            requestAnimationFrame(animate);
        };
        animate();
    }

    /**
     * Process animation queue
     */
    processAnimationQueue() {
        if (this.isAnimating || this.animationQueue.length === 0) return;

        this.isAnimating = true;
        const animation = this.animationQueue.shift();

        animation.execute().then(() => {
            this.isAnimating = false;
        });
    }

    /**
     * Add animation to queue
     * @param {Function} animationFunction - Animation function that returns a promise
     */
    queueAnimation(animationFunction) {
        this.animationQueue.push({
            execute: animationFunction
        });
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Update responsive elements
        this.updateResponsiveLayout();
    }

    /**
     * Update responsive layout
     */
    updateResponsiveLayout() {
        const isMobile = window.innerWidth <= 768;
        const gameContainer = document.getElementById('game-container');

        if (gameContainer) {
            if (isMobile) {
                gameContainer.classList.add('mobile-layout');
            } else {
                gameContainer.classList.remove('mobile-layout');
            }
        }
    }

    /**
     * Handle visibility change
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // Pause animations when tab is not visible
            this.pauseAnimations();
        } else {
            // Resume animations when tab becomes visible
            this.resumeAnimations();
        }
    }

    /**
     * Pause animations
     */
    pauseAnimations() {
        document.body.classList.add('animations-paused');
    }

    /**
     * Resume animations
     */
    resumeAnimations() {
        document.body.classList.remove('animations-paused');
    }

    /**
     * Show loading state
     */
    showLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
        }
    }

    /**
     * Hide loading state
     */
    hideLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
        }
    }

    /**
     * Update UI theme
     * @param {string} theme - Theme name
     */
    updateTheme(theme) {
        document.body.className = `theme-${theme}`;
    }

    /**
     * Get UI statistics
     * @returns {Object} UI statistics
     */
    getUIStatistics() {
        return {
            messagesShown: this.messageQueue.length,
            animationsQueued: this.animationQueue.length,
            isAnimating: this.isAnimating,
            currentTheme: document.body.className
        };
    }
}
