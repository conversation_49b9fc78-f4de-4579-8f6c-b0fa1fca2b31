/**
 * 🎰 SUPER ACE STYLE REEL ENGINE
 * Authentic card dropping mechanics with dynamic value distribution
 * Based on player performance - lose too much = high cards, win too much = low cards
 */

import { CryptoRNG } from "./crypto-rng";

export interface PlayerPerformance {
  totalBet: number;
  totalWon: number;
  winRate: number;
  lossStreak: number;
  winStreak: number;
  recentSpins: boolean[]; // true = win, false = loss
}

export interface ReelDropConfig {
  userId: number;
  betAmount: number;
  playerPerformance: PlayerPerformance;
  shouldWin: boolean;
  winType: 'small' | 'medium' | 'big' | 'mega';
}

export class SuperAceReelEngine {
  private static instance: SuperAceReelEngine;
  
  // 🎯 Card values (higher = better for player)
  private readonly cardValues = {
    '9': 1,
    '10': 2,
    'J': 3,
    'Q': 4,
    'K': 5,
    'A': 6,
    'WILD': 8,
    'SCATTER': 10
  };

  // 🎰 Base card distribution (balanced)
  private readonly baseDistribution = {
    '9': 25,      // Most common, lowest value
    '10': 22,
    'J': 18,
    'Q': 15,
    'K': 12,
    'A': 8,
    'WILD': 5,    // Rare, high value
    'SCATTER': 3  // Rarest, highest value
  };

  public static getInstance(): SuperAceReelEngine {
    if (!SuperAceReelEngine.instance) {
      SuperAceReelEngine.instance = new SuperAceReelEngine();
    }
    return SuperAceReelEngine.instance;
  }

  /**
   * 🎯 Generate authentic Super Ace style reels
   */
  public generateSuperAceReels(config: ReelDropConfig, rng: CryptoRNG): string[][] {
    console.log(`🎰 Super Ace Reel Generation for user ${config.userId}`);
    
    // 1. Analyze player performance
    const performanceAnalysis = this.analyzePlayerPerformance(config.playerPerformance);
    console.log(`📊 Performance Analysis:`, performanceAnalysis);
    
    // 2. Adjust card distribution based on performance
    const adjustedDistribution = this.adjustCardDistribution(performanceAnalysis, config.betAmount);
    console.log(`🎯 Adjusted Distribution:`, adjustedDistribution);
    
    // 3. Generate reels with authentic dropping patterns
    const reels = this.generateAuthenticReels(adjustedDistribution, config, rng);
    
    // 4. Apply final adjustments for win/loss outcome
    const finalReels = this.applyOutcomeAdjustments(reels, config, rng);
    
    console.log(`🎰 Generated Super Ace reels with ${config.shouldWin ? 'WIN' : 'LOSS'} outcome`);
    return finalReels;
  }

  /**
   * 📊 Analyze player performance to determine card quality
   */
  private analyzePlayerPerformance(performance: PlayerPerformance): {
    needsBoost: boolean;
    needsReduction: boolean;
    boostLevel: number; // 1-5 (5 = maximum boost)
    reductionLevel: number; // 1-5 (5 = maximum reduction)
  } {
    const winRate = performance.winRate;
    const lossStreak = performance.lossStreak;
    const winStreak = performance.winStreak;
    
    // Calculate if player needs boost (losing too much)
    const needsBoost = winRate < 0.3 || lossStreak >= 5;
    const boostLevel = Math.min(5, Math.max(1, 
      (lossStreak >= 10 ? 5 : 
       lossStreak >= 7 ? 4 :
       lossStreak >= 5 ? 3 :
       winRate < 0.2 ? 4 :
       winRate < 0.3 ? 2 : 1)
    ));
    
    // Calculate if player needs reduction (winning too much)
    const needsReduction = winRate > 0.7 || winStreak >= 8;
    const reductionLevel = Math.min(5, Math.max(1,
      (winStreak >= 15 ? 5 :
       winStreak >= 10 ? 4 :
       winStreak >= 8 ? 3 :
       winRate > 0.8 ? 4 :
       winRate > 0.7 ? 2 : 1)
    ));

    return {
      needsBoost,
      needsReduction,
      boostLevel,
      reductionLevel
    };
  }

  /**
   * 🎯 Adjust card distribution based on player performance
   */
  private adjustCardDistribution(analysis: any, betAmount: number): Record<string, number> {
    const distribution = { ...this.baseDistribution };
    
    if (analysis.needsBoost) {
      // Player losing too much - increase high-value cards
      const boostMultiplier = 1 + (analysis.boostLevel * 0.3); // Up to 2.5x
      
      distribution['A'] = Math.floor(distribution['A'] * boostMultiplier);
      distribution['K'] = Math.floor(distribution['K'] * boostMultiplier);
      distribution['WILD'] = Math.floor(distribution['WILD'] * boostMultiplier);
      distribution['SCATTER'] = Math.floor(distribution['SCATTER'] * boostMultiplier);
      
      // Reduce low-value cards
      distribution['9'] = Math.floor(distribution['9'] * 0.7);
      distribution['10'] = Math.floor(distribution['10'] * 0.8);
      
      console.log(`🚀 BOOST APPLIED: Level ${analysis.boostLevel} - More high-value cards`);
      
    } else if (analysis.needsReduction) {
      // Player winning too much - increase low-value cards
      const reductionMultiplier = 1 + (analysis.reductionLevel * 0.4); // Up to 3x
      
      distribution['9'] = Math.floor(distribution['9'] * reductionMultiplier);
      distribution['10'] = Math.floor(distribution['10'] * reductionMultiplier);
      distribution['J'] = Math.floor(distribution['J'] * reductionMultiplier);
      
      // Reduce high-value cards
      distribution['A'] = Math.floor(distribution['A'] * 0.5);
      distribution['WILD'] = Math.floor(distribution['WILD'] * 0.3);
      distribution['SCATTER'] = Math.floor(distribution['SCATTER'] * 0.2);
      
      console.log(`⬇️ REDUCTION APPLIED: Level ${analysis.reductionLevel} - More low-value cards`);
    }
    
    // Bet amount adjustments (higher bets = slightly better cards)
    if (betAmount >= 10) {
      distribution['A'] = Math.floor(distribution['A'] * 1.2);
      distribution['K'] = Math.floor(distribution['K'] * 1.1);
    }
    
    return distribution;
  }

  /**
   * 🎰 Generate authentic reel patterns (like Super Ace)
   */
  private generateAuthenticReels(distribution: Record<string, number>, config: ReelDropConfig, rng: CryptoRNG): string[][] {
    const reels: string[][] = [];
    
    // Create weighted symbol pool
    const symbolPool: string[] = [];
    for (const [symbol, weight] of Object.entries(distribution)) {
      for (let i = 0; i < weight; i++) {
        symbolPool.push(symbol);
      }
    }
    
    // Generate 5 reels with 4 rows each (Super Ace style)
    for (let reel = 0; reel < 5; reel++) {
      reels[reel] = [];
      
      for (let row = 0; row < 4; row++) {
        // Authentic random selection
        const randomIndex = Math.floor(rng.next() * symbolPool.length);
        reels[reel][row] = symbolPool[randomIndex];
      }
      
      // Add variety - ensure no reel has all same symbols
      this.ensureReelVariety(reels[reel], symbolPool, rng);
    }
    
    return reels;
  }

  /**
   * 🎲 Ensure each reel has variety (no all-same symbols)
   */
  private ensureReelVariety(reel: string[], symbolPool: string[], rng: CryptoRNG): void {
    const uniqueSymbols = new Set(reel);
    
    // If reel has less than 2 unique symbols, add variety
    if (uniqueSymbols.size < 2) {
      const availableSymbols = Array.from(new Set(symbolPool));
      const currentSymbol = reel[0];
      
      // Replace one position with a different symbol
      const differentSymbols = availableSymbols.filter(s => s !== currentSymbol);
      const newSymbol = differentSymbols[Math.floor(rng.next() * differentSymbols.length)];
      
      const positionToReplace = Math.floor(rng.next() * 4);
      reel[positionToReplace] = newSymbol;
    }
  }

  /**
   * 🎯 Apply final outcome adjustments
   */
  private applyOutcomeAdjustments(reels: string[][], config: ReelDropConfig, rng: CryptoRNG): string[][] {
    if (config.shouldWin) {
      return this.ensureWinningPattern(reels, config.winType, rng);
    } else {
      return this.ensureLosingPattern(reels, rng);
    }
  }

  /**
   * ✅ Ensure winning pattern exists
   */
  private ensureWinningPattern(reels: string[][], winType: string, rng: CryptoRNG): string[][] {
    // Find best symbol for winning line
    const symbols = ['A', 'K', 'Q', 'J', '10', '9'];
    const winSymbol = symbols[Math.floor(rng.next() * symbols.length)];
    
    // Create winning line on a random row
    const winRow = Math.floor(rng.next() * 4);
    const winLength = winType === 'big' || winType === 'mega' ? 5 : 3;
    
    for (let reel = 0; reel < winLength; reel++) {
      reels[reel][winRow] = winSymbol;
    }
    
    console.log(`✅ Created ${winType} win with ${winSymbol} on row ${winRow}`);
    return reels;
  }

  /**
   * ❌ Ensure losing pattern (no wins)
   */
  private ensureLosingPattern(reels: string[][], rng: CryptoRNG): string[][] {
    // Check each row for potential wins and break them
    for (let row = 0; row < 4; row++) {
      let consecutiveCount = 1;
      let currentSymbol = reels[0][row];
      
      for (let reel = 1; reel < 5; reel++) {
        if (reels[reel][row] === currentSymbol || reels[reel][row] === 'WILD' || currentSymbol === 'WILD') {
          consecutiveCount++;
          if (consecutiveCount >= 3) {
            // Break the winning pattern
            const breakSymbols = ['9', '10', 'J', 'Q'];
            const breakSymbol = breakSymbols[Math.floor(rng.next() * breakSymbols.length)];
            reels[reel][row] = breakSymbol;
            consecutiveCount = 1;
            currentSymbol = breakSymbol;
          }
        } else {
          consecutiveCount = 1;
          currentSymbol = reels[reel][row];
        }
      }
    }
    
    console.log(`❌ Ensured losing pattern - no winning lines`);
    return reels;
  }

  /**
   * 📊 Get reel generation statistics
   */
  public getReelStats(): any {
    return {
      cardValues: this.cardValues,
      baseDistribution: this.baseDistribution,
      description: "Super Ace style reel engine with dynamic card distribution based on player performance"
    };
  }
}

// Export singleton instance
export const superAceReelEngine = SuperAceReelEngine.getInstance();
