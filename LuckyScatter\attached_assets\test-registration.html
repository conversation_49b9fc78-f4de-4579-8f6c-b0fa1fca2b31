<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .auth-modal {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .auth-container {
            background: #2a2a3e;
            border: 3px solid #FFD700;
            border-radius: 20px;
            padding: 40px;
            max-width: 450px;
            width: 90%;
        }
        .auth-form h2 {
            color: #FFD700;
            text-align: center;
        }
        .input-group {
            margin-bottom: 20px;
        }
        .input-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #FFD700;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 1.1em;
            box-sizing: border-box;
        }
        .auth-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #FFD700, #FF6B35);
            border: none;
            border-radius: 10px;
            color: #1a1a2e;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .auth-btn:hover {
            transform: translateY(-2px);
        }
        .auth-link {
            text-align: center;
            color: #ccc;
        }
        .auth-link span {
            color: #FFD700;
            cursor: pointer;
            text-decoration: underline;
        }
        #message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 25px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 9999;
            display: none;
        }
        .success { background: #28a745; color: white; }
        .error { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div id="message"></div>
    
    <div class="auth-modal">
        <div class="auth-container">
            <div class="auth-header">
                <h1 style="color: #FFD700; text-align: center;">🎰 REGISTRATION TEST 🎰</h1>
            </div>
            
            <!-- Login Form -->
            <div id="login-form" class="auth-form">
                <h2>🔑 LOGIN</h2>
                <div class="input-group">
                    <input type="tel" id="login-mobile" placeholder="Mobile Number" required>
                </div>
                <div class="input-group">
                    <input type="password" id="login-password" placeholder="Password" required>
                </div>
                <button id="login-btn" class="auth-btn">LOGIN</button>
                <p class="auth-link">Don't have an account? <span id="show-register">Register here</span></p>
            </div>
            
            <!-- Register Form -->
            <div id="register-form" class="auth-form" style="display: none;">
                <h2>📝 REGISTER</h2>
                <div class="input-group">
                    <input type="text" id="register-name" placeholder="Full Name" required>
                </div>
                <div class="input-group">
                    <input type="tel" id="register-mobile" placeholder="Mobile Number" required>
                </div>
                <div class="input-group">
                    <input type="email" id="register-email" placeholder="Email (Optional)">
                </div>
                <div class="input-group">
                    <input type="password" id="register-password" placeholder="Password (min 6 chars)" required>
                </div>
                <div class="input-group">
                    <input type="password" id="register-confirm" placeholder="Confirm Password" required>
                </div>
                <button id="register-btn" class="auth-btn">CREATE ACCOUNT</button>
                <p class="auth-link">Already have an account? <span id="show-login">Login here</span></p>
            </div>
        </div>
    </div>

    <script src="database.js"></script>
    <script src="userManager.js"></script>
    
    <script>
        let userManager;
        
        // Simple message function
        function showMessage(text, type = 'success') {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = type;
            messageEl.style.display = 'block';
            
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }
        
        // Initialize the user management system
        async function initUserSystem() {
            try {
                console.log('🚀 Initializing user system...');
                userManager = new UserManager();
                const initialized = await userManager.init();
                
                if (initialized) {
                    console.log('✅ User system initialized successfully');
                    showMessage('✅ System initialized successfully!', 'success');
                } else {
                    console.error('❌ Failed to initialize user system');
                    showMessage('❌ Failed to initialize system', 'error');
                }
            } catch (error) {
                console.error('❌ Error initializing user system:', error);
                showMessage('❌ System error: ' + error.message, 'error');
            }
        }
        
        // Test functions
        window.testRegistration = function() {
            console.log('🧪 Testing registration system');
            
            // Fill form with test data
            document.getElementById('register-name').value = 'Test User';
            document.getElementById('register-mobile').value = '1234567890';
            document.getElementById('register-email').value = '<EMAIL>';
            document.getElementById('register-password').value = 'password123';
            document.getElementById('register-confirm').value = 'password123';
            
            // Click register button
            document.getElementById('register-btn').click();
        };
        
        window.showRegisterForm = function() {
            document.getElementById('login-form').style.display = 'none';
            document.getElementById('register-form').style.display = 'block';
        };
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 DOM Content Loaded - Initializing test system');
            initUserSystem();
        });
    </script>
</body>
</html>
