import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { User } from "@shared/schema";
import { Play, Minus, Plus, Zap, RotateCcw } from "lucide-react";
import { AudioManager } from "./audio-manager";
import { cascadeManager } from "./cascade-manager";
import { SymbolDisplay } from "./symbol-display";
import { useIsMobile } from "@/hooks/use-mobile";
import "./reel-animation-styles.css";

interface SlotMachineProps {
  user: User;
}

interface GameSession {
  sessionId: string;
  currentBalance: string;
  freeSpinsRemaining: number;
  currentMultiplier: number;
}

interface SpinResult {
  grid: string[][];
  wins: any[];
  totalWin: string;
  newBalance: string;
  multiplier: number;
  scatterTriggered: boolean;
  bonusTriggered: boolean;
  freeSpinsRemaining: number;
  spinId: number;
  winAmount: number;
  winLines: any[];
  scatterCount: number;
  hasBonus: boolean;
  freeSpinsTriggered: number;
}

const betAmounts = [0.10, 0.25, 0.50, 1.00, 2.50, 5.00, 10.00, 25.00, 50.00, 100.00];

export function SlotMachine({ user }: SlotMachineProps) {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [gameSession, setGameSession] = useState<GameSession | null>(null);
  const [currentBet, setCurrentBet] = useState(2.50);
  const [currentBetIndex, setCurrentBetIndex] = useState(4);
  const [isSpinning, setIsSpinning] = useState(false);
  const [reelGrid, setReelGrid] = useState<string[][]>([]);
  const [lastWin, setLastWin] = useState("0.00");
  const [autoSpin, setAutoSpin] = useState(false);
  const [autoSpinCount, setAutoSpinCount] = useState(0);
  const [showWinCelebration, setShowWinCelebration] = useState(false);
  const [winAmount, setWinAmount] = useState("0.00");
  const [showBonusModal, setShowBonusModal] = useState(false);
  const [consecutiveLosses, setConsecutiveLosses] = useState(0);

  // Initialize audio manager
  const audioManager = new AudioManager();

  // Initialize game session
  useEffect(() => {
    if (!gameSession) {
      initializeSession();
    }
  }, [gameSession]);

  // Auto-spin logic
  useEffect(() => {
    if (autoSpin && autoSpinCount > 0 && !isSpinning && gameSession) {
      const balance = parseFloat(gameSession.currentBalance);
      if (balance >= currentBet) {
        const timer = setTimeout(() => {
          performSpin();
          setAutoSpinCount(prev => prev - 1);
        }, 2000);
        return () => clearTimeout(timer);
      } else {
        setAutoSpin(false);
        setAutoSpinCount(0);
        toast({
          title: "Auto-spin stopped",
          description: "Insufficient balance to continue",
          variant: "destructive",
        });
      }
    }
  }, [autoSpin, autoSpinCount, isSpinning, gameSession, currentBet]);

  const initializeSession = async () => {
    console.log("🎮 Initializing game session...");
    try {
      console.log("🎮 Making request to /api/game/start-session");
      const response = await apiRequest("POST", "/api/game/start-session", {});
      console.log("🎮 Session response:", response);
      const session = await response.json();
      console.log("🎮 Session data:", session);
      setGameSession(session);

      // Initialize with random symbols
      const initialGrid: string[][] = [];
      for (let reel = 0; reel < 5; reel++) {
        initialGrid[reel] = [];
        for (let row = 0; row < 4; row++) {
          const symbols = ['9', '10', 'J', 'Q', 'K', 'A', 'SCATTER', 'WILD'];
          initialGrid[reel][row] = symbols[Math.floor(Math.random() * symbols.length)];
        }
      }
      setReelGrid(initialGrid);
      console.log("🎮 Game session initialized successfully!");
    } catch (error) {
      console.error("🎮 Session initialization error:", error);
      toast({
        title: "Session Error",
        description: "Failed to initialize game session",
        variant: "destructive",
      });
    }
  };

  const adjustBet = (direction: 'increase' | 'decrease') => {
    if (direction === 'increase' && currentBetIndex < betAmounts.length - 1) {
      const newIndex = currentBetIndex + 1;
      setCurrentBetIndex(newIndex);
      setCurrentBet(betAmounts[newIndex]);
    } else if (direction === 'decrease' && currentBetIndex > 0) {
      const newIndex = currentBetIndex - 1;
      setCurrentBetIndex(newIndex);
      setCurrentBet(betAmounts[newIndex]);
    }
  };

  const setMaxBet = () => {
    const maxIndex = betAmounts.length - 1;
    setCurrentBetIndex(maxIndex);
    setCurrentBet(betAmounts[maxIndex]);
  };

  const performSpin = async () => {
    console.log("🎰 Spin button clicked!");
    console.log("🎰 Game session:", gameSession);
    console.log("🎰 Is spinning:", isSpinning);

    if (!gameSession || isSpinning) {
      console.log("🎰 Spin blocked - no session or already spinning");
      return;
    }

    const balance = parseFloat(gameSession.currentBalance);
    const isFreeSpinRound = gameSession.freeSpinsRemaining > 0;

    console.log("🎰 Balance:", balance, "Current bet:", currentBet);
    console.log("🎰 Free spin round:", isFreeSpinRound);

    if (!isFreeSpinRound && balance < currentBet) {
      console.log("🎰 Insufficient balance for spin");
      toast({
        title: "Insufficient Balance",
        description: "You don't have enough credits to spin",
        variant: "destructive",
      });
      return;
    }

    console.log("🎰 Starting spin...");
    setIsSpinning(true);
    audioManager.playSpinSound();

    try {
      console.log("🎰 Making API request to /api/game/spin");
      // Call backend spin API FIRST to get final result
      const response = await apiRequest("POST", "/api/game/spin", {
        sessionId: gameSession.sessionId,
        betAmount: currentBet.toFixed(2),
        isFreeSpinRound,
      });

      console.log("🎰 API response received:", response);
      const result: SpinResult = await response.json();
      console.log("🎰 Spin result:", result);

      // Animate reels with final result (simple animation)
      await animateReelsWithResult(result.grid);

      // Handle cascading if enabled
      let finalWinAmount = parseFloat(result.totalWin);
      let finalGrid = result.grid;

      if (result.cascadeCount > 0 && result.wins && result.wins.length > 0) {
        console.log(`🌊 Processing ${result.cascadeCount} cascades`);

        // Process cascades
        const cascadeResult = await cascadeManager.processCascades(
          result.grid,
          result.wins,
          result.cascadeCount
        );

        // Update grid and win amount with cascade results
        finalGrid = cascadeResult.newGrid;
        finalWinAmount += cascadeResult.totalWinAmount;

        console.log(`🌊 Cascades completed! Total cascades: ${cascadeResult.totalCascades}, Extra win: ${cascadeResult.totalWinAmount}`);

        // Show cascade completion message
        if (cascadeResult.totalCascades > 0) {
          toast({
            title: `🌊 ${cascadeResult.totalCascades} Cascades!`,
            description: `Extra wins: ${cascadeResult.totalWinAmount.toFixed(2)} coins!`,
            duration: 4000,
          });
        }
      }

      // Set final grid
      setReelGrid(finalGrid);

      // Update game state immediately after reels stop
      console.log(`💰 Frontend: Updating balance from ${gameSession?.currentBalance} to ${result.newBalance}`);
      setGameSession(prev => prev ? {
        ...prev,
        currentBalance: result.newBalance,
        freeSpinsRemaining: result.freeSpinsRemaining,
        currentMultiplier: result.multiplier,
      } : null);

      setLastWin(finalWinAmount.toFixed(2));

      // Handle wins and update consecutive losses
      if (finalWinAmount > 0) {
        // Reset consecutive losses on win
        setConsecutiveLosses(0);

        audioManager.playWinSound(finalWinAmount, currentBet);
        setWinAmount(finalWinAmount.toFixed(2));

        // Show celebration for big wins
        if (finalWinAmount >= currentBet * 10) {
          setShowWinCelebration(true);
          setTimeout(() => setShowWinCelebration(false), 3000);
        }

        // Highlight winning symbols
        highlightWinningSymbols(result.wins);
      } else {
        // Increment consecutive losses on loss
        setConsecutiveLosses(prev => prev + 1);
        setWinAmount("0.00");
      }

      // Handle bonus triggers
      if (result.bonusTriggered) {
        setShowBonusModal(true);
        setTimeout(() => setShowBonusModal(false), 3000);
      }

      // Update user data in cache
      queryClient.setQueryData(["/api/user"], {
        ...user,
        balance: result.newBalance,
      });

    } catch (error) {
      console.error("🎰 Spin error:", error);

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes("401")) {
        toast({
          title: "Authentication Required",
          description: "Please log in again to continue playing",
          variant: "destructive",
        });
        // Redirect to login
        window.location.href = '/auth';
      } else {
        toast({
          title: "Spin Failed",
          description: `An error occurred while spinning: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        });
      }
    } finally {
      console.log("🎰 Spin completed, setting isSpinning to false");
      setIsSpinning(false);
    }
  };

  // 🎰 Generate completely random and unpredictable drop patterns for 4x4 grid
  const generateVariedDropPattern = () => {
    // Available effects for visual variety
    const allEffects = [
      'none', 'glow', 'pulse', 'bounce', 'spiral', 'flash', 'dramatic',
      'intense', 'climax', 'explosion', 'wave', 'zigzag', 'rubber',
      'shake', 'flip', 'wobble', 'cascade', 'anticipation'
    ];

    // Generate completely random pattern for 4 reels (4x4 grid)
    const numReels = 4;

    // Create multiple entropy sources for maximum randomness
    const timestamp = Date.now();
    const randomSeed1 = Math.floor(Math.random() * 999999);
    const randomSeed2 = Math.floor(Math.random() * 999999);
    const performanceTime = Math.floor(performance.now() * 1000) % 999999;
    const userEntropy = (user?.id || 1) * 7919; // Prime number for better distribution
    const sessionEntropy = gameSession?.sessionId.charCodeAt(0) || 42;

    // Combine all entropy sources
    const masterSeed = timestamp + randomSeed1 + randomSeed2 + performanceTime + userEntropy + sessionEntropy;

    // Generate completely random timings (300ms to 2500ms)
    const timings = [];
    for (let i = 0; i < numReels; i++) {
      const randomTiming = 300 + Math.floor(Math.random() * 2200);
      timings.push(randomTiming);
    }

    // Generate random effects
    const effects = [];
    for (let i = 0; i < numReels; i++) {
      const randomEffect = allEffects[Math.floor(Math.random() * allEffects.length)];
      effects.push(randomEffect);
    }

    // Generate random speeds (0.4x to 2.0x)
    const speeds = [];
    for (let i = 0; i < numReels; i++) {
      const randomSpeed = 0.4 + (Math.random() * 1.6);
      speeds.push(randomSpeed);
    }

    // Randomly choose drop pattern style
    const patternStyles = [
      'Sequential', 'Reverse', 'Random', 'Center-Out', 'Outside-In',
      'Wave-Right', 'Wave-Left', 'Chaos', 'Simultaneous', 'Staggered'
    ];

    const styleIndex = Math.floor(Math.random() * patternStyles.length);
    const selectedStyle = patternStyles[styleIndex];

    // Apply style-specific timing adjustments
    let finalTimings = [...timings];

    switch (selectedStyle) {
      case 'Sequential':
        finalTimings.sort((a, b) => a - b);
        break;
      case 'Reverse':
        finalTimings.sort((a, b) => b - a);
        break;
      case 'Center-Out':
        // Sort and rearrange for center-out pattern
        const sorted = [...finalTimings].sort((a, b) => a - b);
        finalTimings = [sorted[1], sorted[0], sorted[2], sorted[3]];
        break;
      case 'Outside-In':
        const sortedOut = [...finalTimings].sort((a, b) => a - b);
        finalTimings = [sortedOut[0], sortedOut[2], sortedOut[3], sortedOut[1]];
        break;
      case 'Wave-Right':
        finalTimings = finalTimings.map((timing, index) => timing + (index * 100));
        break;
      case 'Wave-Left':
        finalTimings = finalTimings.map((timing, index) => timing + ((numReels - 1 - index) * 100));
        break;
      case 'Simultaneous':
        const avgTiming = finalTimings.reduce((sum, t) => sum + t, 0) / numReels;
        finalTimings = finalTimings.map(() => avgTiming + (Math.random() - 0.5) * 100);
        break;
      case 'Staggered':
        finalTimings = finalTimings.map((timing, index) => timing + (Math.random() * 300));
        break;
      // 'Random' and 'Chaos' keep original random timings
    }

    // Add final micro-variations to prevent exact repetition
    finalTimings = finalTimings.map(timing => {
      const microVariation = (Math.random() - 0.5) * 150;
      return Math.max(250, timing + microVariation);
    });

    const finalSpeeds = speeds.map(speed => {
      const microVariation = (Math.random() - 0.5) * 0.3;
      return Math.max(0.3, Math.min(2.2, speed + microVariation));
    });

    // Generate unique pattern ID
    const patternId = (masterSeed % 999999).toString().padStart(6, '0');
    const patternName = `${selectedStyle}-${patternId}`;

    console.log(`🎰 Generated random 4x4 pattern: ${patternName}`, {
      timings: finalTimings.map(t => Math.round(t)),
      effects: effects,
      speeds: finalSpeeds.map(s => Math.round(s * 100) / 100)
    });

    return {
      timings: finalTimings,
      effects: effects,
      speeds: finalSpeeds,
      name: patternName
    };
  };

  // 🎰 Generate thousands of different symbol patterns during spinning
  const generateSpinningSymbolPatterns = (baseSymbols: string[], patternType: string, userLossStreak: number) => {
    const allSymbols = ['9', '10', 'J', 'Q', 'K', 'A', 'WILD', 'SCATTER'];
    const patterns = [];

    // Generate base number of patterns based on user behavior
    let basePatternCount = 30;
    let highValueChance = 0.3;
    let wildScatterChance = 0.1;

    // Adjust pattern generation based on user loss streak
    if (userLossStreak >= 10) {
      // Heavy losing streak - show more premium symbols for psychological retention
      basePatternCount = 50;
      highValueChance = 0.7;
      wildScatterChance = 0.25;
      console.log(`🎰 Heavy loss streak (${userLossStreak}) - generating premium symbol patterns`);
    } else if (userLossStreak >= 5) {
      // Moderate losing streak - increase high-value symbol frequency
      basePatternCount = 40;
      highValueChance = 0.5;
      wildScatterChance = 0.15;
      console.log(`🎰 Moderate loss streak (${userLossStreak}) - generating balanced patterns`);
    } else if (userLossStreak >= 2) {
      // Light losing streak - standard mix
      basePatternCount = 35;
      highValueChance = 0.4;
      wildScatterChance = 0.12;
    } else {
      // Winning or neutral - more varied patterns
      basePatternCount = 45;
      highValueChance = 0.35;
      wildScatterChance = 0.08;
    }

    // Generate base random patterns
    for (let i = 0; i < basePatternCount; i++) {
      const pattern = [];
      for (let j = 0; j < 16; j++) { // 4x4 grid
        let selectedSymbol;

        const rand = Math.random();
        if (rand < wildScatterChance) {
          // Show WILD or SCATTER
          selectedSymbol = Math.random() < 0.7 ? 'WILD' : 'SCATTER';
        } else if (rand < highValueChance) {
          // Show high-value symbols
          const highValueSymbols = ['K', 'A', 'WILD'];
          selectedSymbol = highValueSymbols[Math.floor(Math.random() * highValueSymbols.length)];
        } else {
          // Show any symbol
          selectedSymbol = allSymbols[Math.floor(Math.random() * allSymbols.length)];
        }

        pattern.push(selectedSymbol);
      }
      patterns.push(pattern);
    }

    // Add pattern-specific themed variations
    switch (patternType) {
      case 'Sequential':
        // Add sequential symbol patterns
        patterns.push(['9', '10', 'J', 'Q', 'K', 'A', 'WILD', 'SCATTER', '9', '10', 'J', 'Q', 'K', 'A', 'WILD', 'SCATTER']);
        patterns.push(['SCATTER', 'WILD', 'A', 'K', 'Q', 'J', '10', '9', 'SCATTER', 'WILD', 'A', 'K', 'Q', 'J', '10', '9']);
        break;

      case 'Chaos':
        // Add completely chaotic patterns
        for (let i = 0; i < 15; i++) {
          const chaosPattern = [];
          for (let j = 0; j < 16; j++) {
            chaosPattern.push(allSymbols[Math.floor(Math.random() * allSymbols.length)]);
          }
          patterns.push(chaosPattern);
        }
        break;

      case 'Center-Out':
        // Add center-focused patterns
        const centerPattern1 = new Array(16).fill('9');
        centerPattern1[5] = centerPattern1[6] = centerPattern1[9] = centerPattern1[10] = 'WILD';
        patterns.push(centerPattern1);

        const centerPattern2 = new Array(16).fill('10');
        centerPattern2[5] = centerPattern2[6] = centerPattern2[9] = centerPattern2[10] = 'SCATTER';
        patterns.push(centerPattern2);
        break;

      case 'Wave-Right':
        // Add wave-like patterns
        const wavePattern = [];
        for (let i = 0; i < 16; i++) {
          const waveIndex = Math.floor(i / 4) + (i % 4);
          wavePattern.push(allSymbols[waveIndex % allSymbols.length]);
        }
        patterns.push(wavePattern);
        break;

      case 'Staggered':
        // Add staggered patterns
        for (let i = 0; i < 8; i++) {
          const staggeredPattern = [];
          for (let j = 0; j < 16; j++) {
            const staggerIndex = (j + i * 3) % allSymbols.length;
            staggeredPattern.push(allSymbols[staggerIndex]);
          }
          patterns.push(staggeredPattern);
        }
        break;
    }

    // Add special "near-miss" patterns for psychological effect
    if (userLossStreak >= 3) {
      // Create patterns that show almost-winning combinations
      for (let i = 0; i < 5; i++) {
        const nearMissPattern = new Array(16).fill('9');
        // Fill most positions with the same high-value symbol
        const targetSymbol = ['K', 'A', 'WILD'][Math.floor(Math.random() * 3)];
        for (let j = 0; j < 14; j++) {
          nearMissPattern[j] = targetSymbol;
        }
        // Add a couple different symbols to break the pattern
        nearMissPattern[14] = allSymbols[Math.floor(Math.random() * allSymbols.length)];
        nearMissPattern[15] = allSymbols[Math.floor(Math.random() * allSymbols.length)];
        patterns.push(nearMissPattern);
      }
    }

    // Shuffle all patterns for maximum unpredictability
    for (let i = patterns.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [patterns[i], patterns[j]] = [patterns[j], patterns[i]];
    }

    console.log(`🎰 Generated ${patterns.length} unique symbol patterns for ${patternType} (Loss streak: ${userLossStreak})`);
    return patterns;
  };

  const animateReelsWithResult = async (finalGrid: string[][]): Promise<void> => {
    return new Promise((resolve) => {
      // Check if we're on mobile or desktop
      const isMobileLayout = isMobile;

      // 🎰 Generate unique drop pattern for this spin
      const dropPattern = generateVariedDropPattern();
      console.log(`🎰 Using drop pattern: ${dropPattern.name}`);

      // Generate thousands of symbol patterns for this spin
      const userLossStreak = consecutiveLosses || 0;
      const spinningPatterns = generateSpinningSymbolPatterns(
        reelGrid.flat(),
        dropPattern.name.split('-')[0],
        userLossStreak
      );
      console.log(`🎰 Generated ${spinningPatterns.length} different symbol patterns for spinning`);

      if (isMobileLayout) {
        // Mobile: animate individual symbol cells with dynamic symbol changing
        const symbolCells = document.querySelectorAll('.mobile-symbol-cell');
        let completedCells = 0;
        const totalCells = Math.min(16, symbolCells.length); // 4x4 grid

        symbolCells.forEach((cell, index) => {
          if (index < totalCells) { // Only animate first 16 cells (4x4)
            console.log(`🎰 Mobile: Animating cell ${index} with dynamic symbols`);
            cell.classList.add('reel-spinning');

            // Apply pattern-specific visual effects
            const effectIndex = index % dropPattern.effects.length;
            if (dropPattern.effects[effectIndex] !== 'none') {
              cell.classList.add(`reel-${dropPattern.effects[effectIndex]}`);
            }

            // 🎰 START DYNAMIC SYMBOL CYCLING
            let patternIndex = 0;
            const symbolCyclingInterval = setInterval(() => {
              if (patternIndex < spinningPatterns.length) {
                const currentPattern = spinningPatterns[patternIndex];
                if (currentPattern[index]) {
                  // Create a temporary grid to trigger React re-render
                  const tempGrid = [...reelGrid];
                  const row = Math.floor(index / 4);
                  const col = index % 4;
                  if (tempGrid[col] && tempGrid[col][row] !== undefined) {
                    tempGrid[col][row] = currentPattern[index];
                    setReelGrid(tempGrid);
                  }
                }
                patternIndex = (patternIndex + 1) % spinningPatterns.length;
              }
            }, 80); // Change symbols every 80ms for visible cycling

            const animationDuration = dropPattern.timings[effectIndex] * dropPattern.speeds[effectIndex];
            console.log(`🎰 Mobile: Cell ${index} will animate for ${animationDuration}ms`);

            setTimeout(() => {
              console.log(`🎰 Mobile: Stopping animation for cell ${index}`);

              // Stop symbol cycling
              clearInterval(symbolCyclingInterval);

              // Remove animation classes
              cell.classList.remove('reel-spinning');
              dropPattern.effects.forEach(effect => {
                if (effect !== 'none') {
                  cell.classList.remove(`reel-${effect}`);
                }
              });

              completedCells++;

              if (completedCells === totalCells) {
                console.log(`🎰 Mobile: All ${totalCells} cells completed animation`);
                // Set final grid immediately when all cells stop
                setReelGrid(finalGrid);
                resolve();
              }
            }, animationDuration);
          }
        });

        // Fallback in case no cells are found
        if (totalCells === 0) {
          setReelGrid(finalGrid);
          resolve();
        }
      } else {
        // Desktop: animate reel columns with dynamic symbol changing
        const reels = document.querySelectorAll('.reel-column');
        let completedReels = 0;
        const maxReels = Math.min(4, reels.length); // Only animate first 4 reels

        reels.forEach((reel, reelIndex) => {
          if (reelIndex < maxReels) { // Only animate first 4 reels
            console.log(`🎰 Desktop: Animating reel ${reelIndex} with dynamic symbols`);
            reel.classList.add('reel-spinning');

            // Apply pattern-specific visual effects
            if (dropPattern.effects[reelIndex] !== 'none') {
              reel.classList.add(`reel-${dropPattern.effects[reelIndex]}`);
            }

            // 🎰 START DYNAMIC SYMBOL CYCLING FOR ENTIRE REEL
            let patternIndex = 0;
            const reelCyclingInterval = setInterval(() => {
              if (patternIndex < spinningPatterns.length) {
                const currentPattern = spinningPatterns[patternIndex];

                // Update the entire reel column in the grid
                const tempGrid = [...reelGrid];
                for (let rowIndex = 0; rowIndex < 4; rowIndex++) {
                  const symbolIndex = reelIndex * 4 + rowIndex; // 4x4 grid calculation
                  if (currentPattern[symbolIndex] && tempGrid[reelIndex]) {
                    tempGrid[reelIndex][rowIndex] = currentPattern[symbolIndex];
                  }
                }
                setReelGrid(tempGrid);

                patternIndex = (patternIndex + 1) % spinningPatterns.length;
              }
            }, 70); // Slightly slower for desktop

            const animationDuration = dropPattern.timings[reelIndex] * dropPattern.speeds[reelIndex];
            console.log(`🎰 Desktop: Reel ${reelIndex} will animate for ${animationDuration}ms`);

            setTimeout(() => {
              console.log(`🎰 Desktop: Stopping animation for reel ${reelIndex}`);

              // Stop symbol cycling
              clearInterval(reelCyclingInterval);

              // Remove animation classes
              reel.classList.remove('reel-spinning');
              dropPattern.effects.forEach(effect => {
                if (effect !== 'none') {
                  reel.classList.remove(`reel-${effect}`);
                }
              });

              completedReels++;

              if (completedReels === maxReels) {
                console.log(`🎰 Desktop: All ${maxReels} reels completed animation`);
                // Set final grid immediately when all reels stop
                setReelGrid(finalGrid);
                resolve();
              }
            }, animationDuration);
          }
        });

        // Fallback in case no reels are found
        if (maxReels === 0) {
          setReelGrid(finalGrid);
          resolve();
        }
      }
    });
  };


  const highlightWinningSymbols = (wins: any[]) => {
    // Clear any existing highlights
    document.querySelectorAll('.winning-symbol').forEach(el => {
      el.classList.remove('winning-symbol');
    });

    // Add highlights for winning positions
    wins.forEach(win => {
      if (win.positions && Array.isArray(win.positions)) {
        win.positions.forEach((pos: { reel: number; row: number }) => {
          const symbolElement = document.querySelector(
            `[data-reel="${pos.reel}"][data-row="${pos.row}"]`
          );
          if (symbolElement) {
            symbolElement.classList.add('winning-symbol');
            // Remove highlight after animation
            setTimeout(() => {
              symbolElement.classList.remove('winning-symbol');
            }, 3000);
          }
        });
      }
    });
  };

  const startAutoSpin = (count: number) => {
    setAutoSpinCount(count);
    setAutoSpin(true);
  };

  const stopAutoSpin = () => {
    setAutoSpin(false);
    setAutoSpinCount(0);
  };

  // Removed getSymbolDisplay - now using SymbolDisplay component

  if (!gameSession) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-casino-gold border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-casino-gold font-orbitron">Initializing game...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Simple CSS animations with cascading */}
      <style>{`
        .reel-spinning {
          animation: spin 0.1s linear infinite;
        }

        .winning-symbol {
          animation: winPulse 1s ease-in-out 3;
          border-color: #ffd700 !important;
          background-color: rgba(255, 215, 0, 0.2) !important;
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
        }

        .cascade-remove {
          animation: cascadeRemove 0.8s ease-in-out forwards;
        }

        .cascade-drop {
          animation: cascadeDrop 0.6s ease-in-out;
        }

        @keyframes spin {
          0% { transform: translateY(0); }
          100% { transform: translateY(-10px); }
        }

        @keyframes winPulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        @keyframes cascadeRemove {
          0% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.8;
          }
          100% {
            transform: scale(0);
            opacity: 0;
          }
        }

        @keyframes cascadeDrop {
          0% {
            transform: translateY(-20px);
            opacity: 0.7;
          }
          50% {
            transform: translateY(5px);
            opacity: 0.9;
          }
          100% {
            transform: translateY(0);
            opacity: 1;
          }
        }
      `}</style>

      {/* Multiplier and Free Spins Displays */}
      <div className="flex justify-between items-start">
        {gameSession.currentMultiplier > 1 && (
          <Card className="glass-card border-casino-gold/50">
            <CardContent className="p-4 text-center">
              <p className="text-xs text-gray-400 uppercase">Multiplier</p>
              <p className="text-2xl font-bold text-casino-gold animate-pulse">
                x{gameSession.currentMultiplier}
              </p>
            </CardContent>
          </Card>
        )}

        {gameSession.freeSpinsRemaining > 0 && (
          <Card className="glass-card border-casino-purple/50">
            <CardContent className="p-4 text-center">
              <p className="text-xs text-gray-400 uppercase">Free Spins</p>
              <p className="text-2xl font-bold text-casino-purple animate-pulse">
                {gameSession.freeSpinsRemaining}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Slot Machine Grid */}
      <Card className={`glass-card border-casino-gold/30 ${isMobile ? 'mobile-slot-container' : 'p-8'}`}>
        {isMobile ? (
          // Mobile 4x4 Grid
          <div className="mobile-slot-grid-4x4">
            {reelGrid.slice(0, 4).map((reel, reelIndex) =>
              reel.slice(0, 4).map((symbol, rowIndex) => (
                <div
                  key={`${reelIndex}-${rowIndex}`}
                  data-reel={reelIndex}
                  data-row={rowIndex}
                  className="mobile-symbol-cell"
                >
                  <SymbolDisplay symbol={symbol} size="small" />
                </div>
              ))
            )}
          </div>
        ) : (
          // Desktop 4x4 Grid (same as mobile now)
          <div className="grid grid-cols-4 gap-3 mb-6">
            {reelGrid.slice(0, 4).map((reel, reelIndex) => (
              <div key={reelIndex} className="reel-column space-y-2">
                {reel.slice(0, 4).map((symbol, rowIndex) => (
                  <div
                    key={`${reelIndex}-${rowIndex}`}
                    data-reel={reelIndex}
                    data-row={rowIndex}
                    className="symbol-container glass-card border border-casino-gold/20 rounded-xl h-20 flex items-center justify-center transition-all duration-300 hover:border-casino-gold/50"
                  >
                    <SymbolDisplay symbol={symbol} size="medium" />
                  </div>
                ))}
              </div>
            ))}
          </div>
        )}

        {/* Game Info */}
        <div className="text-center space-y-2">
          {!isMobile && <p className="text-sm text-gray-400">1024 Ways to Win</p>}
          <div className="h-8 flex items-center justify-center">
            {parseFloat(lastWin) > 0 ? (
              <div className={`font-bold text-casino-gold animate-pulse ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                Last Win: ${lastWin}
              </div>
            ) : (
              <div className={`font-bold text-transparent ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                Last Win: $0.00
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Controls */}
      <Card className={`glass-card border-casino-gold/30 ${isMobile ? 'mobile-controls' : 'p-6'}`}>
        {isMobile ? (
          // Mobile Controls Layout
          <div className="space-y-4">
            {/* Bet Controls */}
            <div className="mobile-bet-controls">
              <button
                onClick={() => adjustBet('decrease')}
                disabled={currentBetIndex === 0 || isSpinning}
                className="mobile-bet-button"
              >
                -
              </button>

              <div className="mobile-bet-display">
                <p className="text-xs text-gray-400 uppercase">BET</p>
                <p className="text-lg font-bold text-casino-gold">
                  ${currentBet.toFixed(2)}
                </p>
              </div>

              <button
                onClick={() => adjustBet('increase')}
                disabled={currentBetIndex === betAmounts.length - 1 || isSpinning}
                className="mobile-bet-button"
              >
                +
              </button>
            </div>

            {/* Spin Button */}
            <button
              onClick={performSpin}
              disabled={isSpinning || (!gameSession.freeSpinsRemaining && parseFloat(gameSession.currentBalance) < currentBet)}
              className="mobile-spin-button"
            >
              {isSpinning ? "SPINNING..." :
               gameSession.freeSpinsRemaining > 0 ? "FREE SPIN" : "SPIN"}
            </button>

            {/* Action Buttons */}
            <div className="mobile-action-buttons">
              <button
                onClick={setMaxBet}
                disabled={isSpinning}
                className="mobile-action-button"
              >
                MAX BET
              </button>
              {!autoSpin ? (
                <button
                  onClick={() => startAutoSpin(25)}
                  disabled={isSpinning}
                  className="mobile-action-button"
                >
                  AUTO 25
                </button>
              ) : (
                <button
                  onClick={stopAutoSpin}
                  className="mobile-action-button active"
                >
                  STOP ({autoSpinCount})
                </button>
              )}
            </div>
          </div>
        ) : (
          // Desktop Controls Layout
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0 lg:space-x-6">
            {/* Bet Controls */}
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => adjustBet('decrease')}
                disabled={currentBetIndex === 0 || isSpinning}
                size="icon"
                className="glass-card border-casino-gold/30 hover:bg-casino-gold/20"
              >
                <Minus className="w-4 h-4 text-casino-gold" />
              </Button>

              <Card className="glass-card border-casino-gold/30 min-w-[120px]">
                <CardContent className="p-3 text-center">
                  <p className="text-xs text-gray-400 uppercase">Bet Amount</p>
                  <p className="text-xl font-bold text-casino-gold">
                    ${currentBet.toFixed(2)}
                  </p>
                </CardContent>
              </Card>

              <Button
                onClick={() => adjustBet('increase')}
                disabled={currentBetIndex === betAmounts.length - 1 || isSpinning}
                size="icon"
                className="glass-card border-casino-gold/30 hover:bg-casino-gold/20"
              >
                <Plus className="w-4 h-4 text-casino-gold" />
              </Button>

              <Button
                onClick={setMaxBet}
                disabled={isSpinning}
                className="bg-casino-red hover:bg-casino-red/80 text-white"
              >
                MAX BET
              </Button>
            </div>

            {/* Spin Button */}
            <Button
              onClick={performSpin}
              disabled={isSpinning || (!gameSession.freeSpinsRemaining && parseFloat(gameSession.currentBalance) < currentBet)}
              className="relative overflow-hidden bg-gradient-to-r from-casino-red via-red-600 to-casino-red hover:from-casino-red/80 hover:via-red-600/80 hover:to-casino-red/80 text-white font-bold px-12 py-6 text-xl"
            >
              <div className="flex items-center space-x-3">
                {isSpinning ? (
                  <RotateCcw className="w-6 h-6 animate-spin" />
                ) : (
                  <Play className="w-6 h-6" />
                )}
                <span>
                  {isSpinning ? "SPINNING..." :
                   gameSession.freeSpinsRemaining > 0 ? "FREE SPIN" : "SPIN"}
                </span>
              </div>
            </Button>

            {/* Auto Spin Controls */}
            <div className="flex items-center space-x-2">
              {!autoSpin ? (
                <>
                  <Button
                    onClick={() => startAutoSpin(10)}
                    disabled={isSpinning}
                    variant="outline"
                    size="sm"
                    className="border-casino-purple text-casino-purple hover:bg-casino-purple/20"
                  >
                    AUTO 10
                  </Button>
                  <Button
                    onClick={() => startAutoSpin(25)}
                    disabled={isSpinning}
                    variant="outline"
                    size="sm"
                    className="border-casino-purple text-casino-purple hover:bg-casino-purple/20"
                  >
                    AUTO 25
                  </Button>
                </>
              ) : (
                <Button
                  onClick={stopAutoSpin}
                  className="bg-casino-purple hover:bg-casino-purple/80 text-white"
                >
                  <Zap className="w-4 h-4 mr-2" />
                  STOP AUTO ({autoSpinCount})
                </Button>
              )}
            </div>

            {/* Game Stats */}
            <Card className="glass-card border-casino-gold/30">
              <CardContent className="p-3 text-center">
                <p className="text-xs text-gray-400 uppercase">Balance</p>
                <p className="text-xl font-bold text-casino-gold">
                  ${parseFloat(gameSession.currentBalance).toFixed(2)}
                </p>
              </CardContent>
            </Card>
          </div>
        )}
      </Card>

      {/* Win Celebration Modal */}
      {showWinCelebration && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-300">
          <Card className="glass-card border-casino-gold/50 p-12 text-center max-w-md mx-4">
            <div className="text-6xl mb-4">🎉</div>
            <div className="text-3xl font-bold text-casino-gold mb-6 uppercase tracking-wide animate-pulse">
              BIG WIN!
            </div>
            <div className="text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-casino-gold via-casino-red to-casino-purple mb-6">
              ${winAmount}
            </div>
            <div className="text-lg text-gray-300">Congratulations!</div>
          </Card>
        </div>
      )}

      {/* Bonus Modal */}
      {showBonusModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-300">
          <Card className="glass-card border-casino-purple/50 p-12 text-center max-w-lg mx-4">
            <div className="text-6xl mb-6">⭐</div>
            <div className="text-4xl font-black text-casino-purple mb-4 uppercase tracking-wide">
              FREE SPINS ACTIVATED!
            </div>
            <div className="text-2xl font-bold text-casino-gold mb-6">
              You won {gameSession.freeSpinsRemaining} Free Spins with {gameSession.currentMultiplier}x Multiplier!
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}

