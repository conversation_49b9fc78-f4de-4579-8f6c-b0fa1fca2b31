/**
 * 🎯 CONSECUTIVE WIN MULTIPLIER SYSTEM
 * Implements progressive multiplier system: 1x → 2x → 3x → 4x → 5x (max)
 * Any loss resets multiplier back to 1x
 */

import { GameSession } from "@shared/schema";

export interface ConsecutiveWinState {
  consecutiveWins: number;
  currentMultiplier: number;
  lastWinAmount: number;
  totalMultipliedWins: number;
}

export class ConsecutiveWinMultiplier {
  private static instance: ConsecutiveWinMultiplier;
  private readonly MAX_MULTIPLIER = 5;
  private readonly MULTIPLIER_MAP = {
    0: 1, // No wins = 1x
    1: 1, // First win = 1x  
    2: 2, // Second consecutive win = 2x
    3: 3, // Third consecutive win = 3x
    4: 4, // Fourth consecutive win = 4x
    5: 5  // Fifth+ consecutive win = 5x (max)
  };

  public static getInstance(): ConsecutiveWinMultiplier {
    if (!ConsecutiveWinMultiplier.instance) {
      ConsecutiveWinMultiplier.instance = new ConsecutiveWinMultiplier();
    }
    return ConsecutiveWinMultiplier.instance;
  }

  /**
   * 🎯 Calculate multiplier based on consecutive wins
   */
  public calculateMultiplier(consecutiveWins: number): number {
    const clampedWins = Math.min(consecutiveWins, this.MAX_MULTIPLIER);
    return this.MULTIPLIER_MAP[clampedWins as keyof typeof this.MULTIPLIER_MAP] || 1;
  }

  /**
   * 🎯 Process win result and update consecutive wins
   */
  public processWinResult(
    session: GameSession,
    baseWinAmount: number,
    isWin: boolean
  ): {
    finalWinAmount: number;
    newConsecutiveWins: number;
    newMultiplier: number;
    multiplierIncreased: boolean;
    resetToBase: boolean;
  } {
    let newConsecutiveWins = session.consecutiveWins || 0;
    let multiplierIncreased = false;
    let resetToBase = false;

    if (isWin && baseWinAmount > 0) {
      // Increment consecutive wins
      newConsecutiveWins++;
      multiplierIncreased = newConsecutiveWins > (session.consecutiveWins || 0);
      
      console.log(`🎯 Consecutive win #${newConsecutiveWins} for session ${session.sessionId}`);
    } else {
      // Reset on any loss
      if (newConsecutiveWins > 0) {
        resetToBase = true;
        console.log(`💥 Consecutive wins reset for session ${session.sessionId} (was ${newConsecutiveWins})`);
      }
      newConsecutiveWins = 0;
    }

    // Calculate new multiplier
    const newMultiplier = this.calculateMultiplier(newConsecutiveWins);
    
    // Apply multiplier to win amount
    const finalWinAmount = isWin ? baseWinAmount * newMultiplier : 0;

    console.log(`🎰 Win processing: Base: $${baseWinAmount.toFixed(2)}, Multiplier: ${newMultiplier}x, Final: $${finalWinAmount.toFixed(2)}`);

    return {
      finalWinAmount,
      newConsecutiveWins,
      newMultiplier,
      multiplierIncreased,
      resetToBase
    };
  }

  /**
   * 🎯 Get multiplier progression info for UI
   */
  public getMultiplierProgression(consecutiveWins: number): {
    current: number;
    next: number | null;
    progress: number; // 0-100%
    isMaxed: boolean;
  } {
    const current = this.calculateMultiplier(consecutiveWins);
    const next = consecutiveWins < this.MAX_MULTIPLIER ? 
      this.calculateMultiplier(consecutiveWins + 1) : null;
    
    const progress = Math.min(100, (consecutiveWins / this.MAX_MULTIPLIER) * 100);
    const isMaxed = consecutiveWins >= this.MAX_MULTIPLIER;

    return {
      current,
      next,
      progress,
      isMaxed
    };
  }

  /**
   * 🎯 Get user-friendly multiplier message
   */
  public getMultiplierMessage(consecutiveWins: number, multiplierIncreased: boolean, resetToBase: boolean): string {
    if (resetToBase) {
      return "💥 Multiplier reset to 1x";
    }
    
    if (multiplierIncreased) {
      const multiplier = this.calculateMultiplier(consecutiveWins);
      if (multiplier >= this.MAX_MULTIPLIER) {
        return `🔥 MAX MULTIPLIER! ${multiplier}x wins!`;
      } else {
        return `🎯 Multiplier increased to ${multiplier}x!`;
      }
    }
    
    const multiplier = this.calculateMultiplier(consecutiveWins);
    return `🎰 Current multiplier: ${multiplier}x`;
  }

  /**
   * 🎯 Check if small bet patterns need more variety
   */
  public shouldIncreaseVariety(betAmount: number, consecutiveWins: number): boolean {
    // For small bets (under $10), increase pattern variety to reduce predictability
    if (betAmount < 10) {
      // If user has no consecutive wins, they might be noticing patterns
      return consecutiveWins === 0;
    }
    return false;
  }

  /**
   * 🎯 Get statistics for admin dashboard
   */
  public getMultiplierStats(): {
    maxMultiplier: number;
    multiplierLevels: number[];
    description: string;
  } {
    return {
      maxMultiplier: this.MAX_MULTIPLIER,
      multiplierLevels: Object.values(this.MULTIPLIER_MAP),
      description: "Progressive multiplier system: 1x → 2x → 3x → 4x → 5x (max). Resets on any loss."
    };
  }
}

// Export singleton instance
export const consecutiveWinMultiplier = ConsecutiveWinMultiplier.getInstance();
