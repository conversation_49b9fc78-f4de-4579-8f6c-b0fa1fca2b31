/**
 * 🎰 VARIED SYMBOL GENERATOR
 * Creates highly varied, unpredictable symbol combinations
 * Prevents repetitive patterns and ensures unique experiences
 */

export interface SymbolPattern {
  id: string;
  symbols: string[];
  weight: number;
  description: string;
}

export interface GenerationConfig {
  shouldWin: boolean;
  winType: 'none' | 'small' | 'medium' | 'big' | 'mega';
  avoidRecentPatterns: boolean;
  userId: number;
  spinCount: number;
}

export class VariedSymbolGenerator {
  private recentPatterns: Map<number, string[]> = new Map(); // userId -> recent pattern hashes
  private maxPatternHistory = 20; // Remember last 20 patterns per user
  
  private symbolPools = {
    common: ['9', '10', 'J', 'Q'],
    uncommon: ['K', 'A'],
    special: ['WILD', 'SCATTER']
  };

  private baseWeights = {
    '9': 25,
    '10': 22,
    'J': 18,
    'Q': 15,
    'K': 12,
    'A': 8,
    'WILD': 3,
    'SCATTER': 2
  };

  /**
   * 🎯 Generate highly varied symbol grid
   */
  public generateVariedGrid(config: GenerationConfig, rng: any): string[][] {
    console.log(`🎰 Generating varied grid for user ${config.userId}, spin ${config.spinCount}`);
    
    const grid: string[][] = [];
    
    // Create base randomization strategy
    const strategy = this.selectRandomizationStrategy(config, rng);
    console.log(`🎰 Using strategy: ${strategy.name}`);

    // Generate each reel with maximum variation
    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = this.generateVariedReel(reel, strategy, config, rng);
    }

    // Apply post-processing for variety
    this.enhanceVariety(grid, config, rng);

    // Check for repetitive patterns and fix them
    if (config.avoidRecentPatterns) {
      this.avoidRecentPatterns(grid, config, rng);
    }

    // Store pattern for future reference
    this.storePattern(config.userId, grid);

    return grid;
  }

  /**
   * 🎲 Select randomization strategy with forced variety
   */
  private selectRandomizationStrategy(config: GenerationConfig, rng: any): any {
    // Force different strategies based on spin count to ensure variety
    const strategyIndex = config.spinCount % 5;

    const strategies = [
      {
        name: 'Pure Random',
        weight: 20,
        symbolDistribution: 'random',
        reelVariation: 'high',
        patternBreaking: true
      },
      {
        name: 'Weighted Chaos',
        weight: 20,
        symbolDistribution: 'weighted',
        reelVariation: 'medium',
        patternBreaking: true
      },
      {
        name: 'Anti-Pattern',
        weight: 20,
        symbolDistribution: 'anti-pattern',
        reelVariation: 'high',
        patternBreaking: true
      },
      {
        name: 'Balanced Mix',
        weight: 20,
        symbolDistribution: 'balanced',
        reelVariation: 'medium',
        patternBreaking: false
      },
      {
        name: 'Surprise Mode',
        weight: 20,
        symbolDistribution: 'surprise',
        reelVariation: 'extreme',
        patternBreaking: true
      }
    ];

    // Use spin count to force strategy rotation for maximum variety
    const selectedStrategy = strategies[strategyIndex];
    console.log(`🎰 Forced strategy selection: ${selectedStrategy.name} (index: ${strategyIndex})`);

    return selectedStrategy;
  }

  /**
   * 🎰 Generate varied reel with unique symbol distribution
   */
  private generateVariedReel(reelIndex: number, strategy: any, config: GenerationConfig, rng: any): string[] {
    const reel: string[] = [];
    
    for (let row = 0; row < 4; row++) {
      let symbol: string;
      
      switch (strategy.symbolDistribution) {
        case 'random':
          symbol = this.getRandomSymbol(rng);
          break;
        case 'weighted':
          symbol = this.getWeightedSymbol(reelIndex, row, rng);
          break;
        case 'anti-pattern':
          symbol = this.getAntiPatternSymbol(reel, reelIndex, row, rng);
          break;
        case 'balanced':
          symbol = this.getBalancedSymbol(reelIndex, row, rng);
          break;
        case 'surprise':
          symbol = this.getSurpriseSymbol(reelIndex, row, rng);
          break;
        default:
          symbol = this.getRandomSymbol(rng);
      }
      
      reel.push(symbol);
    }
    
    return reel;
  }

  /**
   * 🎲 Pure random symbol selection
   */
  private getRandomSymbol(rng: any): string {
    const allSymbols = Object.keys(this.baseWeights);
    return allSymbols[Math.floor(rng.next() * allSymbols.length)];
  }

  /**
   * ⚖️ Weighted symbol selection with variation
   */
  private getWeightedSymbol(reelIndex: number, row: number, rng: any): string {
    // Modify weights based on position for variety
    const modifiedWeights = { ...this.baseWeights };
    
    // Add position-based variation
    const positionFactor = (reelIndex + row) % 3;
    if (positionFactor === 0) {
      // Boost common symbols
      modifiedWeights['9'] *= 1.5;
      modifiedWeights['10'] *= 1.3;
    } else if (positionFactor === 1) {
      // Boost medium symbols
      modifiedWeights['J'] *= 1.4;
      modifiedWeights['Q'] *= 1.4;
      modifiedWeights['K'] *= 1.2;
    } else {
      // Boost rare symbols
      modifiedWeights['A'] *= 1.6;
      modifiedWeights['WILD'] *= 1.8;
    }

    return this.selectFromWeights(modifiedWeights, rng);
  }

  /**
   * 🚫 Anti-pattern symbol selection (avoids creating patterns)
   */
  private getAntiPatternSymbol(currentReel: string[], reelIndex: number, row: number, rng: any): string {
    const allSymbols = Object.keys(this.baseWeights);
    const availableSymbols = [...allSymbols];
    
    // Remove symbols that would create vertical patterns
    if (currentReel.length > 0) {
      const lastSymbol = currentReel[currentReel.length - 1];
      if (currentReel.length >= 2 && currentReel[currentReel.length - 2] === lastSymbol) {
        // Remove the repeating symbol to break vertical pattern
        const index = availableSymbols.indexOf(lastSymbol);
        if (index > -1) availableSymbols.splice(index, 1);
      }
    }
    
    // Ensure we have symbols to choose from
    if (availableSymbols.length === 0) {
      return this.getRandomSymbol(rng);
    }
    
    return availableSymbols[Math.floor(rng.next() * availableSymbols.length)];
  }

  /**
   * ⚖️ Balanced symbol selection
   */
  private getBalancedSymbol(reelIndex: number, row: number, rng: any): string {
    // Ensure good distribution across the grid
    const symbolGroups = [
      this.symbolPools.common,
      this.symbolPools.uncommon,
      this.symbolPools.special
    ];
    
    const groupIndex = (reelIndex + row) % symbolGroups.length;
    const selectedGroup = symbolGroups[groupIndex];
    
    return selectedGroup[Math.floor(rng.next() * selectedGroup.length)];
  }

  /**
   * 🎉 Surprise symbol selection (completely unpredictable)
   */
  private getSurpriseSymbol(reelIndex: number, row: number, rng: any): string {
    // Use time-based seed for extra randomness
    const timeSeed = Date.now() % 1000;
    const extraRandom = (rng.next() + timeSeed / 1000) % 1;
    
    const allSymbols = Object.keys(this.baseWeights);
    const index = Math.floor(extraRandom * allSymbols.length);
    
    return allSymbols[index];
  }

  /**
   * 🎯 Select symbol from weighted distribution
   */
  private selectFromWeights(weights: Record<string, number>, rng: any): string {
    const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
    let random = rng.next() * totalWeight;
    
    for (const [symbol, weight] of Object.entries(weights)) {
      random -= weight;
      if (random <= 0) {
        return symbol;
      }
    }
    
    return Object.keys(weights)[0]; // Fallback
  }

  /**
   * ✨ Enhance variety in the generated grid
   */
  private enhanceVariety(grid: string[][], config: GenerationConfig, rng: any): void {
    // Add random symbol swaps for extra variety
    const swapCount = Math.floor(rng.next() * 3) + 1; // 1-3 swaps
    
    for (let i = 0; i < swapCount; i++) {
      const reel1 = Math.floor(rng.next() * 5);
      const row1 = Math.floor(rng.next() * 4);
      const reel2 = Math.floor(rng.next() * 5);
      const row2 = Math.floor(rng.next() * 4);
      
      // Swap symbols
      const temp = grid[reel1][row1];
      grid[reel1][row1] = grid[reel2][row2];
      grid[reel2][row2] = temp;
    }
  }

  /**
   * 🔄 Avoid recently used patterns with aggressive modification
   */
  private avoidRecentPatterns(grid: string[][], config: GenerationConfig, rng: any): void {
    const currentHash = this.hashGrid(grid);
    const userPatterns = this.recentPatterns.get(config.userId) || [];

    if (userPatterns.includes(currentHash)) {
      console.log(`🎰 Detected recent pattern for user ${config.userId}, applying aggressive modifications...`);

      // Apply more aggressive modifications to ensure uniqueness
      const modifications = Math.floor(rng.next() * 5) + 5; // 5-9 modifications

      for (let i = 0; i < modifications; i++) {
        const reel = Math.floor(rng.next() * 5);
        const row = Math.floor(rng.next() * 4);

        // Use different symbol selection methods for variety
        if (i % 3 === 0) {
          grid[reel][row] = this.getSurpriseSymbol(reel, row, rng);
        } else if (i % 3 === 1) {
          grid[reel][row] = this.getAntiPatternSymbol([], reel, row, rng);
        } else {
          grid[reel][row] = this.getRandomSymbol(rng);
        }
      }

      // Double-check that we created a unique pattern
      const newHash = this.hashGrid(grid);
      if (newHash === currentHash) {
        console.log(`🎰 Pattern still similar, applying final randomization...`);
        // Final randomization - change entire reels
        const reelsToChange = Math.floor(rng.next() * 2) + 2; // 2-3 reels
        for (let i = 0; i < reelsToChange; i++) {
          const reel = Math.floor(rng.next() * 5);
          for (let row = 0; row < 4; row++) {
            grid[reel][row] = this.getRandomSymbol(rng);
          }
        }
      }
    }
  }

  /**
   * 🔢 Create hash of grid pattern
   */
  private hashGrid(grid: string[][]): string {
    return grid.map(reel => reel.join('')).join('|');
  }

  /**
   * 💾 Store pattern for future reference
   */
  private storePattern(userId: number, grid: string[][]): void {
    const hash = this.hashGrid(grid);
    
    if (!this.recentPatterns.has(userId)) {
      this.recentPatterns.set(userId, []);
    }
    
    const userPatterns = this.recentPatterns.get(userId)!;
    userPatterns.push(hash);
    
    // Keep only recent patterns
    if (userPatterns.length > this.maxPatternHistory) {
      userPatterns.shift();
    }
  }

  /**
   * 📊 Get variety statistics
   */
  public getVarietyStats(userId: number): any {
    const userPatterns = this.recentPatterns.get(userId) || [];
    const uniquePatterns = new Set(userPatterns).size;
    
    return {
      totalPatterns: userPatterns.length,
      uniquePatterns,
      varietyScore: userPatterns.length > 0 ? (uniquePatterns / userPatterns.length) * 100 : 100
    };
  }
}

export const variedSymbolGenerator = new VariedSymbolGenerator();
