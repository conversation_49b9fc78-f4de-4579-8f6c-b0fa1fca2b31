/**
 * 🎯 ADVANCED SCATTER SYSTEM - Emotional Timing & Anticipation
 * Implements proper scatter mechanics with 2-scatter anticipation and progressive rewards
 */

import { naturalLuckEngine } from "./natural-luck-engine";

export interface ScatterResult {
  scatterCount: number;
  positions: { reel: number; row: number }[];
  anticipationTriggered: boolean;
  freeSpins: number;
  multiplier: number;
  megaJackpot: boolean;
  stickyWilds: boolean;
}

export interface AnticipationState {
  userId: number;
  reelsToStop: number[];
  currentReel: number;
  scatterPositions: { reel: number; row: number }[];
  anticipationLevel: 'none' | 'building' | 'intense' | 'climax';
}

export class AdvancedScatterSystem {
  private anticipationStates = new Map<number, AnticipationState>();
  
  /**
   * 🎯 Generate scatter result with emotional timing
   */
  public generateScatterResult(
    userId: number, 
    betAmount: number, 
    grid: string[][]
  ): ScatterResult {
    const scatterChance = naturalLuckEngine.calculateScatterChance(userId, betAmount);
    const shouldTriggerScatter = Math.random() < scatterChance;
    
    if (!shouldTriggerScatter) {
      // Check for 2-scatter anticipation
      return this.checkForAnticipation(userId, grid);
    }
    
    // Generate full scatter bonus
    const scatterCount = naturalLuckEngine.determineScatterCount(userId, betAmount);
    return this.createScatterBonus(userId, scatterCount, betAmount);
  }

  /**
   * 🎭 Check for 2-scatter anticipation effect
   */
  private checkForAnticipation(userId: number, grid: string[][]): ScatterResult {
    const existingScatters = this.countScattersInGrid(grid);
    
    // 30% chance to show 2 scatters for anticipation
    if (existingScatters < 2 && Math.random() < 0.3) {
      return this.create2ScatterAnticipation(userId, grid);
    }
    
    return {
      scatterCount: existingScatters,
      positions: this.getScatterPositions(grid),
      anticipationTriggered: false,
      freeSpins: 0,
      multiplier: 1,
      megaJackpot: false,
      stickyWilds: false,
    };
  }

  /**
   * 🎭 Create 2-scatter anticipation sequence
   */
  private create2ScatterAnticipation(userId: number, grid: string[][]): ScatterResult {
    // Place exactly 2 scatters in strategic positions
    const positions = [
      { reel: 0, row: Math.floor(Math.random() * 4) },
      { reel: 2, row: Math.floor(Math.random() * 4) },
    ];
    
    // Update grid with scatters
    positions.forEach(pos => {
      grid[pos.reel][pos.row] = 'SCATTER';
    });
    
    // Set up anticipation state for next reel
    this.anticipationStates.set(userId, {
      userId,
      reelsToStop: [4], // Last reel for maximum suspense
      currentReel: 0,
      scatterPositions: positions,
      anticipationLevel: 'building',
    });
    
    return {
      scatterCount: 2,
      positions,
      anticipationTriggered: true,
      freeSpins: 0,
      multiplier: 1,
      megaJackpot: false,
      stickyWilds: false,
    };
  }

  /**
   * 🎁 Create full scatter bonus
   */
  private createScatterBonus(userId: number, scatterCount: number, betAmount: number): ScatterResult {
    const positions = this.generateScatterPositions(scatterCount);
    const { freeSpins, multiplier } = this.calculateScatterRewards(scatterCount, userId, betAmount);
    const megaJackpot = scatterCount === 5;
    const stickyWilds = scatterCount >= 4; // 4+ scatters get sticky wilds
    
    // Update luck engine
    naturalLuckEngine.updatePlayerState(userId, true, 0, betAmount, true, megaJackpot);
    
    return {
      scatterCount,
      positions,
      anticipationTriggered: false,
      freeSpins,
      multiplier,
      megaJackpot,
      stickyWilds,
    };
  }

  /**
   * 🎯 Calculate scatter rewards based on count
   */
  private calculateScatterRewards(
    scatterCount: number, 
    userId: number, 
    betAmount: number
  ): { freeSpins: number; multiplier: number } {
    const luckScore = naturalLuckEngine.calculateLuckScore(userId, betAmount);
    
    let baseFreeSpins = 0;
    let baseMultiplier = 1;
    
    switch (scatterCount) {
      case 3:
        baseFreeSpins = 10;
        baseMultiplier = 2;
        break;
      case 4:
        baseFreeSpins = 15;
        baseMultiplier = 3;
        break;
      case 5:
        baseFreeSpins = 25;
        baseMultiplier = 5;
        break;
    }
    
    // 🍀 Luck-based bonus spins
    const bonusSpins = Math.floor(luckScore * 5); // Up to 5 extra spins
    const totalFreeSpins = Math.min(baseFreeSpins + bonusSpins, baseFreeSpins + 5);
    
    return {
      freeSpins: totalFreeSpins,
      multiplier: baseMultiplier,
    };
  }

  /**
   * 🎯 Generate scatter positions on grid
   */
  private generateScatterPositions(count: number): { reel: number; row: number }[] {
    const positions: { reel: number; row: number }[] = [];
    const usedReels = new Set<number>();
    
    // Ensure scatters are spread across different reels
    while (positions.length < count && usedReels.size < 5) {
      const reel = Math.floor(Math.random() * 5);
      if (!usedReels.has(reel)) {
        positions.push({
          reel,
          row: Math.floor(Math.random() * 4),
        });
        usedReels.add(reel);
      }
    }
    
    // If we need more scatters than reels, add to existing reels
    while (positions.length < count) {
      const reel = Math.floor(Math.random() * 5);
      const existingInReel = positions.filter(p => p.reel === reel).length;
      
      if (existingInReel < 4) { // Max 4 per reel
        positions.push({
          reel,
          row: Math.floor(Math.random() * 4),
        });
      }
    }
    
    return positions;
  }

  /**
   * 🎯 Count scatters in existing grid
   */
  private countScattersInGrid(grid: string[][]): number {
    let count = 0;
    for (let reel = 0; reel < 5; reel++) {
      for (let row = 0; row < 4; row++) {
        if (grid[reel][row] === 'SCATTER') {
          count++;
        }
      }
    }
    return count;
  }

  /**
   * 🎯 Get scatter positions from grid
   */
  private getScatterPositions(grid: string[][]): { reel: number; row: number }[] {
    const positions: { reel: number; row: number }[] = [];
    
    for (let reel = 0; reel < 5; reel++) {
      for (let row = 0; row < 4; row++) {
        if (grid[reel][row] === 'SCATTER') {
          positions.push({ reel, row });
        }
      }
    }
    
    return positions;
  }

  /**
   * 🎭 Process anticipation sequence
   */
  public processAnticipationSequence(userId: number): {
    shouldPause: boolean;
    pauseDuration: number;
    soundEffect: 'rising_tone' | 'suspense' | 'climax' | 'none';
    visualEffect: 'glow' | 'pulse' | 'shake' | 'none';
    message: string;
  } {
    const anticipation = this.anticipationStates.get(userId);
    
    if (!anticipation) {
      return {
        shouldPause: false,
        pauseDuration: 0,
        soundEffect: 'none',
        visualEffect: 'none',
        message: '',
      };
    }
    
    switch (anticipation.anticipationLevel) {
      case 'building':
        anticipation.anticipationLevel = 'intense';
        return {
          shouldPause: true,
          pauseDuration: 1000, // 1 second pause
          soundEffect: 'rising_tone',
          visualEffect: 'glow',
          message: '🍀 Two scatters landed... Need one more!',
        };
        
      case 'intense':
        anticipation.anticipationLevel = 'climax';
        return {
          shouldPause: true,
          pauseDuration: 1500, // 1.5 second pause
          soundEffect: 'suspense',
          visualEffect: 'pulse',
          message: '⭐ This could be it... Final reel spinning!',
        };
        
      case 'climax':
        this.anticipationStates.delete(userId); // Clean up
        return {
          shouldPause: true,
          pauseDuration: 500, // 0.5 second pause
          soundEffect: 'climax',
          visualEffect: 'shake',
          message: '🎯 The moment of truth!',
        };
        
      default:
        return {
          shouldPause: false,
          pauseDuration: 0,
          soundEffect: 'none',
          visualEffect: 'none',
          message: '',
        };
    }
  }

  /**
   * 🎯 Check if player should get third scatter (for anticipation resolution)
   */
  public shouldCompleteAnticipation(userId: number, betAmount: number): boolean {
    const anticipation = this.anticipationStates.get(userId);
    if (!anticipation) return false;
    
    const luckScore = naturalLuckEngine.calculateLuckScore(userId, betAmount);
    
    // Higher luck score = better chance to complete the scatter
    const completionChance = 0.2 + (luckScore * 0.3); // 20-50% chance
    
    return Math.random() < completionChance;
  }

  /**
   * 🎯 Apply sticky wilds during free spins
   */
  public applyStickyWilds(grid: string[][], wildCount: number): { reel: number; row: number }[] {
    if (wildCount < 3) return [];
    
    const stickyPositions: { reel: number; row: number }[] = [];
    
    // Find existing wilds and make them sticky
    for (let reel = 0; reel < 5; reel++) {
      for (let row = 0; row < 4; row++) {
        if (grid[reel][row] === 'WILD') {
          stickyPositions.push({ reel, row });
        }
      }
    }
    
    return stickyPositions;
  }

  /**
   * 🎯 Calculate free spin win with multiplier
   */
  public calculateFreeSpinWin(
    baseWin: number, 
    multiplier: number, 
    stickyWildCount: number
  ): number {
    let totalMultiplier = multiplier;
    
    // Sticky wilds add extra multiplier
    if (stickyWildCount >= 3) {
      totalMultiplier += Math.floor(stickyWildCount / 3); // +1x per 3 sticky wilds
    }
    
    return baseWin * totalMultiplier;
  }

  /**
   * 🎯 Get anticipation state for UI
   */
  public getAnticipationState(userId: number): AnticipationState | null {
    return this.anticipationStates.get(userId) || null;
  }

  /**
   * 🎯 Clear anticipation state
   */
  public clearAnticipationState(userId: number): void {
    this.anticipationStates.delete(userId);
  }

  /**
   * 🎯 Get scatter statistics for debugging
   */
  public getScatterStats(userId: number): {
    anticipationActive: boolean;
    anticipationLevel: string;
    scatterChance: number;
    luckScore: number;
  } {
    const anticipation = this.anticipationStates.get(userId);
    const scatterChance = naturalLuckEngine.calculateScatterChance(userId, 10);
    const luckScore = naturalLuckEngine.calculateLuckScore(userId, 10);
    
    return {
      anticipationActive: !!anticipation,
      anticipationLevel: anticipation?.anticipationLevel || 'none',
      scatterChance,
      luckScore,
    };
  }
}

// Export singleton instance
export const advancedScatterSystem = new AdvancedScatterSystem();
