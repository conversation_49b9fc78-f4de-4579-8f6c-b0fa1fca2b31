/* Symbol Display Styles */
.symbol-display {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.symbol-display img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.2s ease;
}

.symbol-display img:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 4px 8px rgba(255, 215, 0, 0.4));
}

/* Fallback text styling */
.symbol-display .symbol-text {
  font-family: 'Orbitron', monospace;
  font-weight: bold;
  text-shadow: 0 0 10px currentColor;
  transition: all 0.2s ease;
}

.symbol-display .symbol-text:hover {
  transform: scale(1.1);
  text-shadow: 0 0 15px currentColor;
}

/* Winning symbol animations */
.winning-symbol .symbol-display img,
.winning-symbol .symbol-display .symbol-text {
  animation: symbolWin 1s ease-in-out infinite;
}

@keyframes symbolWin {
  0%, 100% { 
    transform: scale(1); 
    filter: brightness(1);
  }
  50% { 
    transform: scale(1.15); 
    filter: brightness(1.3) drop-shadow(0 0 20px #ffd700);
  }
}

/* Symbol rarity effects */
.symbol-display[data-symbol="SCATTER"] img,
.symbol-display[data-symbol="SCATTER"] .symbol-text {
  filter: drop-shadow(0 0 8px #ffd700);
}

.symbol-display[data-symbol="WILD"] img,
.symbol-display[data-symbol="WILD"] .symbol-text {
  filter: drop-shadow(0 0 8px #ff4444);
}

.symbol-display[data-symbol="A"] img,
.symbol-display[data-symbol="A"] .symbol-text {
  filter: drop-shadow(0 0 6px #ffd700);
}
