/**
 * SymbolsManager.js - Symbol handling, weights, and transformations
 * Manages symbol generation, wild transformations, and symbol properties
 */

class SymbolsManager {
    /**
     * Initialize the symbols manager
     * @param {Phaser.Scene} scene - The Phaser scene
     * @param {Object} config - Game configuration
     */
    constructor(scene, config) {
        this.scene = scene;
        this.config = config;

        // Symbol definitions with weights for 97% RTP
        this.symbolWeights = [
            { symbol: '9', weight: 45, payout: [0, 0, 2, 10, 50] },
            { symbol: '10', weight: 40, payout: [0, 0, 3, 15, 75] },
            { symbol: 'J', weight: 35, payout: [0, 0, 4, 20, 100] },
            { symbol: 'Q', weight: 30, payout: [0, 0, 5, 25, 125] },
            { symbol: 'K', weight: 25, payout: [0, 0, 6, 30, 150] },
            { symbol: 'A', weight: 20, payout: [0, 0, 8, 40, 200] },
            { symbol: 'SCATTER', weight: 3, payout: [0, 0, 2, 8, 75] },
            { symbol: 'GOLDEN_CARD', weight: 2, payout: [0, 0, 20, 75, 300] }
        ];

        // Special symbols
        this.specialSymbols = {
            SCATTER: 'SCATTER',
            GOLDEN_CARD: 'GOLDEN_CARD',
            LITTLE_JOKER: 'LITTLE_JOKER',
            BIG_JOKER: 'BIG_JOKER'
        };

        // Wild transformation probabilities
        this.wildTransformChances = {
            GOLDEN_TO_LITTLE_JOKER: 0.05, // 5%
            GOLDEN_TO_BIG_JOKER: 0.02     // 2%
        };

        // Create weighted symbol pool
        this.createSymbolPool();
    }

    /**
     * Create weighted symbol pool for random selection
     */
    createSymbolPool() {
        this.symbolPool = [];

        this.symbolWeights.forEach(symbolData => {
            for (let i = 0; i < symbolData.weight; i++) {
                this.symbolPool.push(symbolData.symbol);
            }
        });

        // Shuffle the pool for better randomness
        this.shuffleArray(this.symbolPool);
    }

    /**
     * Shuffle array using Fisher-Yates algorithm
     * @param {Array} array - Array to shuffle
     */
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    /**
     * Get a random symbol from the weighted pool
     * @returns {string} Random symbol
     */
    getRandomSymbol() {
        const randomIndex = Math.floor(Math.random() * this.symbolPool.length);
        let symbol = this.symbolPool[randomIndex];

        // Check for wild transformations
        symbol = this.checkWildTransformation(symbol);

        return symbol;
    }

    /**
     * Check if a symbol should transform into a wild
     * @param {string} symbol - Original symbol
     * @returns {string} Transformed symbol or original
     */
    checkWildTransformation(symbol) {
        if (symbol === this.specialSymbols.GOLDEN_CARD) {
            const random = Math.random();

            if (random < this.wildTransformChances.GOLDEN_TO_BIG_JOKER) {
                return this.specialSymbols.BIG_JOKER;
            } else if (random < this.wildTransformChances.GOLDEN_TO_BIG_JOKER + this.wildTransformChances.GOLDEN_TO_LITTLE_JOKER) {
                return this.specialSymbols.LITTLE_JOKER;
            }
        }

        return symbol;
    }

    /**
     * Get symbol payout multiplier
     * @param {string} symbol - Symbol name
     * @param {number} count - Number of symbols in winning combination
     * @returns {number} Payout multiplier
     */
    getSymbolPayout(symbol, count) {
        const symbolData = this.symbolWeights.find(s => s.symbol === symbol);

        if (!symbolData || count < 3 || count > 5) {
            return 0;
        }

        return symbolData.payout[count] || 0;
    }

    /**
     * Check if symbol is wild
     * @param {string} symbol - Symbol to check
     * @returns {boolean} True if symbol is wild
     */
    isWild(symbol) {
        return symbol === this.specialSymbols.LITTLE_JOKER ||
               symbol === this.specialSymbols.BIG_JOKER;
    }

    /**
     * Check if symbol is scatter
     * @param {string} symbol - Symbol to check
     * @returns {boolean} True if symbol is scatter
     */
    isScatter(symbol) {
        return symbol === this.specialSymbols.SCATTER;
    }

    /**
     * Check if symbol can substitute for another
     * @param {string} wildSymbol - Wild symbol
     * @param {string} targetSymbol - Target symbol to substitute
     * @returns {boolean} True if substitution is valid
     */
    canSubstitute(wildSymbol, targetSymbol) {
        if (!this.isWild(wildSymbol)) {
            return false;
        }

        // Wilds cannot substitute for scatters
        if (this.isScatter(targetSymbol)) {
            return false;
        }

        return true;
    }

    /**
     * Get all symbols that match for winning combinations
     * @param {string} symbol1 - First symbol
     * @param {string} symbol2 - Second symbol
     * @returns {boolean} True if symbols match
     */
    symbolsMatch(symbol1, symbol2) {
        // Exact match
        if (symbol1 === symbol2) {
            return true;
        }

        // Wild substitution
        if (this.isWild(symbol1) && this.canSubstitute(symbol1, symbol2)) {
            return true;
        }

        if (this.isWild(symbol2) && this.canSubstitute(symbol2, symbol1)) {
            return true;
        }

        return false;
    }

    /**
     * Expand wild symbols on reels
     * @param {Array} symbolGrid - Current symbol grid
     * @param {number} reel - Reel index
     * @returns {Array} Updated symbol grid
     */
    expandWild(symbolGrid, reel) {
        const newGrid = JSON.parse(JSON.stringify(symbolGrid));

        // Check if reel contains Big Joker
        let hasBigJoker = false;
        for (let row = 0; row < this.config.rows; row++) {
            if (newGrid[reel][row] === this.specialSymbols.BIG_JOKER) {
                hasBigJoker = true;
                break;
            }
        }

        // Expand Big Joker to entire reel
        if (hasBigJoker) {
            for (let row = 0; row < this.config.rows; row++) {
                newGrid[reel][row] = this.specialSymbols.BIG_JOKER;
            }
        }

        return newGrid;
    }

    /**
     * Get symbol display properties
     * @param {string} symbol - Symbol name
     * @returns {Object} Display properties
     */
    getSymbolProperties(symbol) {
        const properties = {
            '9': { color: '#8B4513', glow: false, special: false },
            '10': { color: '#CD853F', glow: false, special: false },
            'J': { color: '#4169E1', glow: false, special: false },
            'Q': { color: '#9932CC', glow: false, special: false },
            'K': { color: '#DC143C', glow: false, special: false },
            'A': { color: '#FFD700', glow: true, special: false },
            'SCATTER': { color: '#FFD700', glow: true, special: true },
            'GOLDEN_CARD': { color: '#FFA500', glow: true, special: true },
            'LITTLE_JOKER': { color: '#FF69B4', glow: true, special: true },
            'BIG_JOKER': { color: '#FF1493', glow: true, special: true }
        };

        return properties[symbol] || properties['9'];
    }

    /**
     * Generate symbol strip for reel animation
     * @param {number} length - Length of symbol strip
     * @returns {Array} Array of symbols
     */
    generateSymbolStrip(length = 20) {
        const strip = [];

        for (let i = 0; i < length; i++) {
            strip.push(this.getRandomSymbol());
        }

        return strip;
    }

    /**
     * Calculate symbol frequency in current grid
     * @param {Array} symbolGrid - Current symbol grid
     * @returns {Object} Symbol frequency map
     */
    calculateSymbolFrequency(symbolGrid) {
        const frequency = {};

        for (let reel = 0; reel < this.config.reels; reel++) {
            for (let row = 0; row < this.config.rows; row++) {
                const symbol = symbolGrid[reel][row];
                if (symbol) {
                    frequency[symbol] = (frequency[symbol] || 0) + 1;
                }
            }
        }

        return frequency;
    }

    /**
     * Get symbol rarity level
     * @param {string} symbol - Symbol name
     * @returns {string} Rarity level
     */
    getSymbolRarity(symbol) {
        const symbolData = this.symbolWeights.find(s => s.symbol === symbol);

        if (!symbolData) {
            return 'common';
        }

        if (symbolData.weight >= 25) return 'common';
        if (symbolData.weight >= 15) return 'uncommon';
        if (symbolData.weight >= 5) return 'rare';
        return 'legendary';
    }

    /**
     * Validate symbol grid
     * @param {Array} symbolGrid - Symbol grid to validate
     * @returns {boolean} True if grid is valid
     */
    validateSymbolGrid(symbolGrid) {
        if (!Array.isArray(symbolGrid)) return false;
        if (symbolGrid.length !== this.config.reels) return false;

        for (let reel = 0; reel < this.config.reels; reel++) {
            if (!Array.isArray(symbolGrid[reel])) return false;
            if (symbolGrid[reel].length !== this.config.rows) return false;

            for (let row = 0; row < this.config.rows; row++) {
                const symbol = symbolGrid[reel][row];
                if (!symbol || typeof symbol !== 'string') return false;
            }
        }

        return true;
    }

    /**
     * Get weighted random symbol for specific position
     * @param {number} reel - Reel index
     * @param {number} row - Row index
     * @returns {string} Symbol for position
     */
    getPositionalSymbol(reel, row) {
        // Could implement position-specific weights here
        // For now, use standard random symbol
        return this.getRandomSymbol();
    }

    /**
     * Create symbol animation data
     * @param {string} symbol - Symbol name
     * @returns {Object} Animation configuration
     */
    getSymbolAnimation(symbol) {
        const baseAnimation = {
            duration: 300,
            ease: 'Power2.easeOut'
        };

        if (this.isWild(symbol)) {
            return {
                ...baseAnimation,
                scale: { from: 0.8, to: 1.2 },
                glow: true,
                particles: true
            };
        }

        if (this.isScatter(symbol)) {
            return {
                ...baseAnimation,
                rotation: { from: 0, to: Math.PI * 2 },
                glow: true
            };
        }

        return baseAnimation;
    }

    /**
     * Get symbol sound effect
     * @param {string} symbol - Symbol name
     * @returns {string} Sound effect name
     */
    getSymbolSound(symbol) {
        if (this.isWild(symbol)) return 'wild_sound';
        if (this.isScatter(symbol)) return 'scatter_sound';

        const rarity = this.getSymbolRarity(symbol);
        return `${rarity}_symbol_sound`;
    }
}
