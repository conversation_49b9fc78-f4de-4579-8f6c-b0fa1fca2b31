/**
 * 🧠 ADVANCED AI SLOT ENGINE
 * Implements sophisticated casino psychology and adaptive RTP mechanics
 * Based on modern slot machine AI research and industry best practices
 */

export interface PlayerPsychProfile {
  userId: number;
  totalSpins: number;
  totalWagered: number;
  totalWon: number;
  sessionBalance: number;
  sessionStartBalance: number; // Track starting balance for loss percentage calculation
  currentStreak: number; // positive = wins, negative = losses
  spinsSinceLastBonus: number;
  spinsSinceLastBigWin: number;
  avgBetSize: number;
  sessionTime: number;
  lastActivity: Date;
  riskTolerance: 'low' | 'medium' | 'high';
  playStyle: 'casual' | 'aggressive' | 'whale';
  retentionRisk: 'low' | 'medium' | 'high';
  nearMissCount: number;
  ldwCount: number; // Loss-Disguised-as-Win count
  adaptiveWinBoostActive: boolean; // Track if 50% loss boost is active
  lossPercentage: number; // Current session loss percentage
}

export interface AdvancedGameDecision {
  shouldWin: boolean;
  winMultiplier: number;
  outcomeType: 'big_win' | 'small_win' | 'near_miss' | 'ldw' | 'loss';
  symbolWeightAdjustments: { [symbol: string]: number };
  bonusForced: boolean;
  psychologicalEffect: 'celebration' | 'near_miss_excitement' | 'anticipation' | 'neutral';
  reason: string;
}

export class AdvancedAISlotEngine {
  private playerProfiles: Map<number, PlayerPsychProfile> = new Map();
  
  // AI Configuration
  private readonly TARGET_RTP = 0.85; // 85% return to player
  private readonly MAX_LOSS_STREAK = 12; // Force intervention after 12 losses
  private readonly MAX_SPINS_WITHOUT_BONUS = 50; // Force bonus after 50 spins
  private readonly NEAR_MISS_FREQUENCY = 0.15; // 15% of losing spins should be near-misses
  private readonly LDW_FREQUENCY = 0.25; // 25% of spins should be LDW when possible
  
  // Base symbol weights (will be adjusted by bet amount)
  private baseSymbolWeights = {
    '9': 20,      // 20% base chance
    '10': 18,     // 18% base chance
    'J': 16,      // 16% base chance
    'Q': 14,      // 14% base chance
    'K': 12,      // 12% base chance
    'A': 10,      // 10% base chance
    'WILD': 6,    // 6% base chance
    'SCATTER': 4  // 4% base chance (reduced from previous)
  };

  // Bet tier thresholds (like Super Ace)
  private betTiers = {
    low: { min: 0.10, max: 2.49 },      // $0.10 - $2.49
    medium: { min: 2.50, max: 9.99 },   // $2.50 - $9.99
    high: { min: 10.00, max: 49.99 },   // $10.00 - $49.99
    vip: { min: 50.00, max: 999.99 }    // $50.00+
  };

  /**
   * 💰 Get bet tier based on amount (Super Ace style)
   */
  private getBetTier(betAmount: number): 'low' | 'medium' | 'high' | 'vip' {
    if (betAmount >= this.betTiers.vip.min) return 'vip';
    if (betAmount >= this.betTiers.high.min) return 'high';
    if (betAmount >= this.betTiers.medium.min) return 'medium';
    return 'low';
  }

  /**
   * 🎰 Calculate bet-adjusted symbol weights (higher bets = higher value cards)
   */
  private getBetAdjustedWeights(betAmount: number): { [symbol: string]: number } {
    const tier = this.getBetTier(betAmount);
    const weights = { ...this.baseSymbolWeights };

    switch (tier) {
      case 'low': // $0.10 - $2.49: More low-value cards
        weights['9'] = 25;    // Increase low cards
        weights['10'] = 22;
        weights['J'] = 18;
        weights['Q'] = 15;
        weights['K'] = 10;    // Reduce high cards
        weights['A'] = 6;
        weights['WILD'] = 3;
        weights['SCATTER'] = 1;
        break;

      case 'medium': // $2.50 - $9.99: Balanced distribution
        weights['9'] = 18;
        weights['10'] = 16;
        weights['J'] = 16;
        weights['Q'] = 14;
        weights['K'] = 12;
        weights['A'] = 10;
        weights['WILD'] = 6;
        weights['SCATTER'] = 4;
        break;

      case 'high': // $10.00 - $49.99: More high-value cards
        weights['9'] = 12;    // Reduce low cards
        weights['10'] = 14;
        weights['J'] = 16;
        weights['Q'] = 18;
        weights['K'] = 16;    // Increase high cards
        weights['A'] = 14;
        weights['WILD'] = 8;
        weights['SCATTER'] = 6;
        break;

      case 'vip': // $50.00+: Premium cards dominate
        weights['9'] = 8;     // Minimal low cards
        weights['10'] = 10;
        weights['J'] = 14;
        weights['Q'] = 16;
        weights['K'] = 20;    // High cards dominate
        weights['A'] = 18;
        weights['WILD'] = 12;
        weights['SCATTER'] = 8;
        break;
    }

    console.log(`💰 Bet tier: ${tier.toUpperCase()} ($${betAmount}) - Adjusted weights:`, weights);
    return weights;
  }

  /**
   * 🧠 Main AI decision engine with advanced psychology
   */
  public makeAdvancedGameDecision(userId: number, betAmount: number): AdvancedGameDecision {
    const profile = this.getPlayerProfile(userId);
    this.updatePlayerProfile(profile, betAmount);

    console.log(`🧠 AI analyzing player ${userId}:`, {
      streak: profile.currentStreak,
      rtp: this.calculateRTP(profile),
      retentionRisk: profile.retentionRisk,
      spinsSinceBonus: profile.spinsSinceLastBonus,
      playStyle: profile.playStyle,
      lossPercentage: profile.lossPercentage,
      adaptiveBoostActive: profile.adaptiveWinBoostActive
    });

    // Step 1: Check for 50% loss adaptive boost (NEW FEATURE)
    const adaptiveBoostDecision = this.checkAdaptiveLossBoost(profile, betAmount);
    if (adaptiveBoostDecision) {
      console.log(`🚀 Adaptive Loss Boost: ${adaptiveBoostDecision.reason}`);
      return adaptiveBoostDecision;
    }

    // Step 2: Check for forced interventions
    const forcedDecision = this.checkForcedInterventions(profile, betAmount);
    if (forcedDecision) {
      console.log(`🎯 Forced intervention: ${forcedDecision.reason}`);
      return forcedDecision;
    }

    // Step 3: Apply adaptive RTP logic
    const adaptiveDecision = this.applyAdaptiveRTP(profile, betAmount);
    if (adaptiveDecision) {
      console.log(`📊 Adaptive RTP: ${adaptiveDecision.reason}`);
      return adaptiveDecision;
    }

    // Step 4: Apply psychological engagement mechanics
    const psychDecision = this.applyPsychologicalMechanics(profile, betAmount);
    console.log(`🧠 Psychological decision: ${psychDecision.reason}`);
    return psychDecision;
  }

  /**
   * 🚀 NEW: Check for 50% loss adaptive boost (User's requested feature)
   * If player has lost more than 50% of session balance, increase win rate by 30%
   */
  private checkAdaptiveLossBoost(profile: PlayerPsychProfile, betAmount: number): AdvancedGameDecision | null {
    // Calculate current loss percentage
    if (profile.sessionStartBalance > 0) {
      const currentLoss = profile.sessionStartBalance - profile.sessionBalance;
      profile.lossPercentage = (currentLoss / profile.sessionStartBalance) * 100;

      // Activate boost if player has lost more than 50%
      if (profile.lossPercentage >= 50 && !profile.adaptiveWinBoostActive) {
        profile.adaptiveWinBoostActive = true;
        console.log(`🚀 ADAPTIVE BOOST ACTIVATED: Player ${profile.userId} has lost ${profile.lossPercentage.toFixed(1)}% of session balance`);
      }

      // Apply 30% increased win rate when boost is active
      if (profile.adaptiveWinBoostActive) {
        const boostedWinChance = 0.3 + 0.3; // Base 30% + 30% boost = 60% win chance
        const shouldWin = Math.random() < boostedWinChance;

        if (shouldWin) {
          // Give frequent small wins to prevent feeling of losing everything
          const isSmallWin = Math.random() < 0.7; // 70% chance for small win, 30% for bigger win

          if (isSmallWin) {
            return {
              shouldWin: true,
              winMultiplier: 0.8 + Math.random() * 0.7, // 0.8x to 1.5x (small but frequent wins)
              outcomeType: 'small_win',
              symbolWeightAdjustments: this.createSmallWinWeights(betAmount),
              bonusForced: false,
              psychologicalEffect: 'celebration',
              reason: `🚀 Adaptive boost small win (${profile.lossPercentage.toFixed(1)}% session loss) - keeping player engaged`
            };
          } else {
            return {
              shouldWin: true,
              winMultiplier: 1.5 + Math.random() * 1.5, // 1.5x to 3x (bigger recovery win)
              outcomeType: 'big_win',
              symbolWeightAdjustments: this.createWinningWeights(betAmount),
              bonusForced: false,
              psychologicalEffect: 'celebration',
              reason: `🚀 Adaptive boost recovery win (${profile.lossPercentage.toFixed(1)}% session loss) - helping player recover`
            };
          }
        } else {
          // Even on "losses", give near-misses to maintain hope
          return {
            shouldWin: false,
            winMultiplier: 1,
            outcomeType: 'near_miss',
            symbolWeightAdjustments: this.createNearMissWeights(betAmount),
            bonusForced: false,
            psychologicalEffect: 'near_miss_excitement',
            reason: `🚀 Adaptive boost near-miss (${profile.lossPercentage.toFixed(1)}% session loss) - maintaining hope`
          };
        }
      }
    }

    return null;
  }

  /**
   * 🚨 Check for forced interventions (prevent extreme streaks)
   */
  private checkForcedInterventions(profile: PlayerPsychProfile, betAmount: number): AdvancedGameDecision | null {
    // Force win after extreme loss streak
    if (profile.currentStreak <= -this.MAX_LOSS_STREAK) {
      return {
        shouldWin: true,
        winMultiplier: 2.5 + Math.random() * 2.5, // 2.5x to 5x
        outcomeType: 'big_win',
        symbolWeightAdjustments: this.createWinningWeights(betAmount),
        bonusForced: false,
        psychologicalEffect: 'celebration',
        reason: `🚨 Forced win after ${Math.abs(profile.currentStreak)} losses (retention risk)`
      };
    }

    // Force bonus after too many spins
    if (profile.spinsSinceLastBonus >= this.MAX_SPINS_WITHOUT_BONUS) {
      return {
        shouldWin: true,
        winMultiplier: 1.5 + Math.random() * 1.5, // 1.5x to 3x
        outcomeType: 'big_win',
        symbolWeightAdjustments: this.createBonusWeights(betAmount),
        bonusForced: true,
        psychologicalEffect: 'celebration',
        reason: `🎁 Forced bonus after ${profile.spinsSinceLastBonus} spins`
      };
    }

    // Prevent back-to-back big wins (house protection)
    if (profile.spinsSinceLastBigWin <= 2 && this.calculateRTP(profile) > this.TARGET_RTP + 0.15) {
      return {
        shouldWin: false,
        winMultiplier: 1,
        outcomeType: 'near_miss',
        symbolWeightAdjustments: this.createNearMissWeights(betAmount),
        bonusForced: false,
        psychologicalEffect: 'near_miss_excitement',
        reason: `🛡️ Preventing consecutive big wins (house protection)`
      };
    }

    return null;
  }

  /**
   * 📊 Apply adaptive RTP based on player state
   */
  private applyAdaptiveRTP(profile: PlayerPsychProfile, betAmount: number): AdvancedGameDecision | null {
    const currentRTP = this.calculateRTP(profile);
    const rtpDifference = currentRTP - this.TARGET_RTP;

    // Player RTP too low - boost wins
    if (rtpDifference < -0.1) {
      return {
        shouldWin: true,
        winMultiplier: 1.2 + Math.random() * 0.8, // 1.2x to 2x
        outcomeType: 'small_win',
        symbolWeightAdjustments: this.createSmallWinWeights(betAmount),
        bonusForced: false,
        psychologicalEffect: 'celebration',
        reason: `📈 RTP boost (current: ${(currentRTP * 100).toFixed(1)}%, target: ${(this.TARGET_RTP * 100).toFixed(1)}%)`
      };
    }

    // Player RTP too high - reduce wins but use psychology
    if (rtpDifference > 0.1) {
      // Use LDW or near-miss instead of pure loss
      const useLDW = Math.random() < this.LDW_FREQUENCY;
      if (useLDW) {
        return {
          shouldWin: true,
          winMultiplier: 0.3 + Math.random() * 0.4, // 0.3x to 0.7x (net loss but feels like win)
          outcomeType: 'ldw',
          symbolWeightAdjustments: this.createLDWWeights(betAmount),
          bonusForced: false,
          psychologicalEffect: 'celebration',
          reason: `🎭 LDW (Loss-Disguised-as-Win) - RTP control with psychology`
        };
      } else {
        return {
          shouldWin: false,
          winMultiplier: 1,
          outcomeType: 'near_miss',
          symbolWeightAdjustments: this.createNearMissWeights(betAmount),
          bonusForced: false,
          psychologicalEffect: 'near_miss_excitement',
          reason: `🎯 Near-miss for engagement - RTP control`
        };
      }
    }

    return null;
  }

  /**
   * 🧠 Apply psychological engagement mechanics
   */
  private applyPsychologicalMechanics(profile: PlayerPsychProfile, betAmount: number): AdvancedGameDecision {
    // High retention risk - use engagement tactics
    if (profile.retentionRisk === 'high') {
      const useNearMiss = Math.random() < this.NEAR_MISS_FREQUENCY;
      if (useNearMiss) {
        return {
          shouldWin: false,
          winMultiplier: 1,
          outcomeType: 'near_miss',
          symbolWeightAdjustments: this.createNearMissWeights(betAmount),
          bonusForced: false,
          psychologicalEffect: 'near_miss_excitement',
          reason: `🎯 Near-miss for high-risk player engagement`
        };
      }
    }

    // Whale players - need bigger excitement
    if (profile.playStyle === 'whale') {
      const shouldWin = Math.random() < 0.4; // 40% win chance
      if (shouldWin) {
        return {
          shouldWin: true,
          winMultiplier: 2 + Math.random() * 4, // 2x to 6x
          outcomeType: 'big_win',
          symbolWeightAdjustments: this.createWinningWeights(betAmount),
          bonusForced: false,
          psychologicalEffect: 'celebration',
          reason: `🐋 Whale player - big win for excitement`
        };
      }
    }

    // Default behavior with psychological elements
    const shouldWin = Math.random() < 0.3; // 30% base win chance
    if (shouldWin) {
      const useLDW = Math.random() < this.LDW_FREQUENCY && profile.currentStreak < 0;
      if (useLDW) {
        return {
          shouldWin: true,
          winMultiplier: 0.4 + Math.random() * 0.4, // 0.4x to 0.8x
          outcomeType: 'ldw',
          symbolWeightAdjustments: this.createLDWWeights(betAmount),
          bonusForced: false,
          psychologicalEffect: 'celebration',
          reason: `🎭 LDW for psychological engagement`
        };
      } else {
        return {
          shouldWin: true,
          winMultiplier: 1.1 + Math.random() * 1.4, // 1.1x to 2.5x
          outcomeType: 'small_win',
          symbolWeightAdjustments: this.createSmallWinWeights(betAmount),
          bonusForced: false,
          psychologicalEffect: 'celebration',
          reason: `🎉 Regular small win`
        };
      }
    } else {
      const useNearMiss = Math.random() < this.NEAR_MISS_FREQUENCY;
      if (useNearMiss) {
        return {
          shouldWin: false,
          winMultiplier: 1,
          outcomeType: 'near_miss',
          symbolWeightAdjustments: this.createNearMissWeights(betAmount),
          bonusForced: false,
          psychologicalEffect: 'near_miss_excitement',
          reason: `🎯 Near-miss for engagement`
        };
      } else {
        return {
          shouldWin: false,
          winMultiplier: 1,
          outcomeType: 'loss',
          symbolWeightAdjustments: {},
          bonusForced: false,
          psychologicalEffect: 'neutral',
          reason: `❌ Regular loss`
        };
      }
    }
  }

  /**
   * 🎯 Create symbol weights for different outcome types (bet-adjusted)
   */
  private createWinningWeights(betAmount: number): { [symbol: string]: number } {
    const baseWeights = this.getBetAdjustedWeights(betAmount);
    const tier = this.getBetTier(betAmount);

    // Boost high-value symbols for wins, scaled by bet tier
    const multiplier = tier === 'vip' ? 1.5 : tier === 'high' ? 1.3 : tier === 'medium' ? 1.1 : 1.0;

    return {
      'A': Math.round(baseWeights['A'] * multiplier * 1.5),
      'K': Math.round(baseWeights['K'] * multiplier * 1.3),
      'Q': Math.round(baseWeights['Q'] * multiplier * 1.2),
      'WILD': Math.round(baseWeights['WILD'] * multiplier * 1.4),
      'SCATTER': 2  // Keep scatters rare regardless of bet
    };
  }

  private createBonusWeights(betAmount: number): { [symbol: string]: number } {
    const baseWeights = this.getBetAdjustedWeights(betAmount);
    const tier = this.getBetTier(betAmount);

    // Higher bets get more bonus chances
    const scatterBoost = tier === 'vip' ? 25 : tier === 'high' ? 20 : tier === 'medium' ? 15 : 10;

    return {
      'SCATTER': scatterBoost,
      'WILD': Math.round(baseWeights['WILD'] * 1.2),
      'A': Math.round(baseWeights['A'] * 1.1)
    };
  }

  private createNearMissWeights(betAmount: number): { [symbol: string]: number } {
    const baseWeights = this.getBetAdjustedWeights(betAmount);

    // Show appropriate value symbols based on bet tier
    return {
      'A': Math.round(baseWeights['A'] * 1.2),
      'K': Math.round(baseWeights['K'] * 1.2),
      'Q': Math.round(baseWeights['Q'] * 1.1),
      'WILD': Math.round(baseWeights['WILD'] * 0.8),  // Reduce wilds to prevent accidental wins
      'SCATTER': Math.round(baseWeights['SCATTER'] * 1.5)  // Show scatters but not enough for bonus
    };
  }

  private createLDWWeights(betAmount: number): { [symbol: string]: number } {
    const baseWeights = this.getBetAdjustedWeights(betAmount);
    const tier = this.getBetTier(betAmount);

    // LDW should use lower-value symbols relative to bet tier
    if (tier === 'vip') {
      return {
        'J': 25, 'Q': 20, 'K': 15, 'WILD': 5, 'SCATTER': 1
      };
    } else if (tier === 'high') {
      return {
        '10': 25, 'J': 20, 'Q': 15, 'WILD': 5, 'SCATTER': 1
      };
    } else {
      return {
        '9': 25, '10': 20, 'J': 15, 'WILD': 5, 'SCATTER': 1
      };
    }
  }

  private createSmallWinWeights(betAmount: number): { [symbol: string]: number } {
    const baseWeights = this.getBetAdjustedWeights(betAmount);

    // Small wins use medium-value symbols for the bet tier
    return {
      'J': Math.round(baseWeights['J'] * 1.2),
      'Q': Math.round(baseWeights['Q'] * 1.1),
      'K': Math.round(baseWeights['K'] * 1.0),
      'WILD': Math.round(baseWeights['WILD'] * 0.9),
      'SCATTER': Math.round(baseWeights['SCATTER'] * 0.8)
    };
  }

  /**
   * 👤 Player profile management
   */
  private getPlayerProfile(userId: number): PlayerPsychProfile {
    if (!this.playerProfiles.has(userId)) {
      this.playerProfiles.set(userId, {
        userId,
        totalSpins: 0,
        totalWagered: 0,
        totalWon: 0,
        sessionBalance: 0,
        sessionStartBalance: 0, // Initialize session start balance
        currentStreak: 0,
        spinsSinceLastBonus: 0,
        spinsSinceLastBigWin: 0,
        avgBetSize: 0,
        sessionTime: 0,
        lastActivity: new Date(),
        riskTolerance: 'medium',
        playStyle: 'casual',
        retentionRisk: 'low',
        nearMissCount: 0,
        ldwCount: 0,
        adaptiveWinBoostActive: false, // Initialize adaptive boost as inactive
        lossPercentage: 0 // Initialize loss percentage
      });
    }
    return this.playerProfiles.get(userId)!;
  }

  private updatePlayerProfile(profile: PlayerPsychProfile, betAmount: number): void {
    profile.totalSpins++;
    profile.totalWagered += betAmount;
    profile.avgBetSize = profile.totalWagered / profile.totalSpins;
    profile.lastActivity = new Date();
    profile.spinsSinceLastBonus++;
    profile.spinsSinceLastBigWin++;

    // Update play style based on betting patterns
    if (profile.avgBetSize >= 50) {
      profile.playStyle = 'whale';
    } else if (profile.avgBetSize >= 10) {
      profile.playStyle = 'aggressive';
    } else {
      profile.playStyle = 'casual';
    }

    // Update retention risk
    if (profile.currentStreak <= -8) {
      profile.retentionRisk = 'high';
    } else if (profile.currentStreak <= -4) {
      profile.retentionRisk = 'medium';
    } else {
      profile.retentionRisk = 'low';
    }
  }

  private calculateRTP(profile: PlayerPsychProfile): number {
    if (profile.totalWagered === 0) return 0;
    return profile.totalWon / profile.totalWagered;
  }

  /**
   * 🚀 Initialize session for adaptive loss tracking
   */
  public initializeSession(userId: number, startBalance: number): void {
    const profile = this.getPlayerProfile(userId);
    profile.sessionStartBalance = startBalance;
    profile.sessionBalance = startBalance;
    profile.adaptiveWinBoostActive = false;
    profile.lossPercentage = 0;

    console.log(`🚀 Session initialized for player ${userId} with balance: $${startBalance}`);
  }

  /**
   * 💰 Update session balance after spin
   */
  public updateSessionBalance(userId: number, betAmount: number, winAmount: number): void {
    const profile = this.getPlayerProfile(userId);
    profile.sessionBalance = profile.sessionBalance - betAmount + winAmount;

    // Calculate loss percentage
    if (profile.sessionStartBalance > 0) {
      const currentLoss = profile.sessionStartBalance - profile.sessionBalance;
      profile.lossPercentage = Math.max(0, (currentLoss / profile.sessionStartBalance) * 100);
    }

    console.log(`💰 Player ${userId} balance updated: $${profile.sessionBalance.toFixed(2)} (${profile.lossPercentage.toFixed(1)}% loss)`);
  }

  /**
   * 📊 Update player state after spin result
   */
  public updatePlayerState(userId: number, won: boolean, winAmount: number, outcomeType: string): void {
    const profile = this.getPlayerProfile(userId);

    if (won) {
      profile.currentStreak = Math.max(0, profile.currentStreak) + 1;
      profile.totalWon += winAmount;

      // Track big wins
      if (outcomeType === 'big_win') {
        profile.spinsSinceLastBigWin = 0;
      }

      // Track LDW
      if (outcomeType === 'ldw') {
        profile.ldwCount++;
      }
    } else {
      profile.currentStreak = Math.min(0, profile.currentStreak) - 1;

      // Track near misses
      if (outcomeType === 'near_miss') {
        profile.nearMissCount++;
      }
    }

    // Track bonus triggers
    if (outcomeType === 'big_win' && winAmount > profile.avgBetSize * 3) {
      profile.spinsSinceLastBonus = 0;
    }

    console.log(`📊 Player ${userId} updated:`, {
      streak: profile.currentStreak,
      rtp: this.calculateRTP(profile).toFixed(3),
      retentionRisk: profile.retentionRisk,
      nearMisses: profile.nearMissCount,
      ldws: profile.ldwCount,
      lossPercentage: profile.lossPercentage.toFixed(1) + '%',
      adaptiveBoostActive: profile.adaptiveWinBoostActive
    });
  }

  /**
   * 🎰 Generate grid with dynamic symbol weighting (bet-adjusted)
   */
  public generateAdvancedGrid(decision: AdvancedGameDecision, betAmount: number, rng: any): string[][] {
    const grid: string[][] = [];

    // Start with bet-adjusted base weights, then apply decision adjustments
    const baseWeights = this.getBetAdjustedWeights(betAmount);
    const activeWeights = { ...baseWeights };
    Object.entries(decision.symbolWeightAdjustments).forEach(([symbol, weight]) => {
      activeWeights[symbol] = weight;
    });

    // Create weighted symbol pool
    const symbolPool: string[] = [];
    Object.entries(activeWeights).forEach(([symbol, weight]) => {
      for (let i = 0; i < weight; i++) {
        symbolPool.push(symbol);
      }
    });

    // Generate grid with weighted selection
    for (let reel = 0; reel < 5; reel++) {
      grid[reel] = [];
      for (let row = 0; row < 4; row++) {
        const randomIndex = Math.floor(rng.next() * symbolPool.length);
        grid[reel][row] = symbolPool[randomIndex];
      }
    }

    // Apply outcome-specific modifications
    if (decision.outcomeType === 'near_miss') {
      this.createNearMissPattern(grid, rng);
    } else if (decision.shouldWin && decision.outcomeType !== 'ldw') {
      this.ensureWinningPattern(grid, decision.winMultiplier, rng);
    }

    return grid;
  }

  /**
   * 🎯 Create near-miss patterns for psychological engagement
   */
  private createNearMissPattern(grid: string[][], rng: any): void {
    // Create a pattern that looks like it almost won
    const highValueSymbols = ['A', 'K', 'Q'];
    const targetSymbol = highValueSymbols[Math.floor(rng.next() * highValueSymbols.length)];

    // Place 2 matching symbols in first two reels
    const row = Math.floor(rng.next() * 4);
    grid[0][row] = targetSymbol;
    grid[1][row] = targetSymbol;

    // Place a different but visually similar symbol in third reel
    const nearSymbols = { 'A': 'K', 'K': 'Q', 'Q': 'J' };
    grid[2][row] = nearSymbols[targetSymbol as keyof typeof nearSymbols] || 'J';
  }

  /**
   * 🏆 Ensure winning patterns exist
   */
  private ensureWinningPattern(grid: string[][], multiplier: number, rng: any): void {
    const symbols = ['A', 'K', 'Q', 'J'];
    const winSymbol = symbols[Math.floor(rng.next() * symbols.length)];
    const row = Math.floor(rng.next() * 4);

    // Create winning line based on multiplier
    const reelCount = multiplier >= 3 ? 5 : multiplier >= 2 ? 4 : 3;
    for (let reel = 0; reel < reelCount; reel++) {
      grid[reel][row] = winSymbol;
    }
  }

  /**
   * 📈 Get player statistics for monitoring
   */
  public getPlayerStats(userId: number): any {
    const profile = this.getPlayerProfile(userId);
    return {
      totalSpins: profile.totalSpins,
      rtp: this.calculateRTP(profile),
      currentStreak: profile.currentStreak,
      playStyle: profile.playStyle,
      retentionRisk: profile.retentionRisk,
      avgBetSize: profile.avgBetSize,
      nearMissCount: profile.nearMissCount,
      ldwCount: profile.ldwCount,
      spinsSinceLastBonus: profile.spinsSinceLastBonus,
      // 🚀 NEW: Adaptive boost statistics
      lossPercentage: profile.lossPercentage,
      adaptiveWinBoostActive: profile.adaptiveWinBoostActive,
      sessionStartBalance: profile.sessionStartBalance,
      sessionBalance: profile.sessionBalance
    };
  }
}

export const advancedAISlotEngine = new AdvancedAISlotEngine();
