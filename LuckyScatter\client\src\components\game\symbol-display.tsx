/**
 * SymbolDisplay Component
 * Displays slot machine symbols with image support and fallback to text/emoji
 */

import React, { useState } from 'react';

interface SymbolDisplayProps {
  symbol: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export const SymbolDisplay: React.FC<SymbolDisplayProps> = ({ 
  symbol, 
  size = 'medium', 
  className = '' 
}) => {
  const [imageError, setImageError] = useState(false);

  // Symbol configuration with image paths and fallbacks
  const symbolConfig: { [key: string]: { 
    imagePath: string; 
    emoji: string; 
    color: string;
    name: string;
  } } = {
    '9': { 
      imagePath: '/assets/symbols/9.png', 
      emoji: '9', 
      color: 'text-gray-300',
      name: 'Nine'
    },
    '10': { 
      imagePath: '/assets/symbols/10.png', 
      emoji: '10', 
      color: 'text-gray-300',
      name: 'Ten'
    },
    'J': { 
      imagePath: '/assets/symbols/J.png', 
      emoji: 'J', 
      color: 'text-blue-400',
      name: 'Jack'
    },
    'Q': { 
      imagePath: '/assets/symbols/Q.png', 
      emoji: 'Q', 
      color: 'text-purple-400',
      name: 'Queen'
    },
    'K': { 
      imagePath: '/assets/symbols/K.png', 
      emoji: 'K', 
      color: 'text-red-400',
      name: 'King'
    },
    'A': { 
      imagePath: '/assets/symbols/A.png', 
      emoji: 'A', 
      color: 'text-casino-gold',
      name: 'Ace'
    },
    'WILD': { 
      imagePath: '/assets/symbols/WILD.png', 
      emoji: '🃏', 
      color: 'text-casino-red',
      name: 'Wild'
    },
    'SCATTER': { 
      imagePath: '/assets/symbols/SCATTER.png', 
      emoji: '⭐', 
      color: 'text-casino-gold',
      name: 'Scatter'
    },
  };

  // Size configurations
  const sizeConfig = {
    small: {
      container: 'w-12 h-12',
      image: 'w-10 h-10',
      text: 'text-lg',
    },
    medium: {
      container: 'w-16 h-16',
      image: 'w-14 h-14',
      text: 'text-2xl',
    },
    large: {
      container: 'w-20 h-20',
      image: 'w-18 h-18',
      text: 'text-3xl',
    },
  };

  const config = symbolConfig[symbol];
  const sizes = sizeConfig[size];

  if (!config) {
    return (
      <div className={`${sizes.container} flex items-center justify-center ${className}`}>
        <span className={`${sizes.text} text-gray-500 font-bold`}>?</span>
      </div>
    );
  }

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div
      className={`symbol-display ${sizes.container} flex items-center justify-center ${className}`}
      title={config.name}
      data-symbol={symbol}
    >
      {!imageError ? (
        <img
          src={config.imagePath}
          alt={config.name}
          className={`${sizes.image} object-contain transition-all duration-200 hover:scale-110`}
          onError={handleImageError}
          loading="lazy"
          style={{
            filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))',
          }}
        />
      ) : (
        <span
          className={`symbol-text ${sizes.text} ${config.color} font-bold transition-all duration-200`}
          style={{
            textShadow: '0 0 10px currentColor',
            fontFamily: 'Orbitron, monospace',
          }}
        >
          {config.emoji}
        </span>
      )}
    </div>
  );
};

export default SymbolDisplay;
