import { pgTable, text, serial, integer, boolean, decimal, timestamp, jsonb, date } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  mobile: text("mobile").notNull().unique(),
  password: text("password").notNull(),
  balance: decimal("balance", { precision: 10, scale: 2 }).notNull().default("1000.00"),
  totalDeposits: decimal("total_deposits", { precision: 10, scale: 2 }).notNull().default("0.00"),
  totalWithdrawals: decimal("total_withdrawals", { precision: 10, scale: 2 }).notNull().default("0.00"),
  totalSpins: integer("total_spins").notNull().default(0),
  totalWins: decimal("total_wins", { precision: 10, scale: 2 }).notNull().default("0.00"),
  joinDate: timestamp("join_date", { withTimezone: true }).notNull().defaultNow(),
  lastLogin: timestamp("last_login", { withTimezone: true }),
  isActive: boolean("is_active").notNull().default(true),
  role: text("role").notNull().default("user"), // "user" or "admin"

  // 🧠 Psychology & Engagement Fields
  luckScore: integer("luck_score").notNull().default(50), // 0-100, affects win chances
  vipLevel: text("vip_level").notNull().default("bronze"), // bronze, silver, gold, diamond
  vipPoints: integer("vip_points").notNull().default(0), // Points for VIP progression
  loginStreak: integer("login_streak").notNull().default(0), // Daily login streak
  lastLoginDate: date("last_login_date"), // For tracking daily logins
  freeSpinsRemaining: integer("free_spins_remaining").notNull().default(10), // Daily free spins for new users
  lastFreeSpinDate: date("last_free_spin_date"), // Track daily free spin usage

  // 🎰 Game Psychology Tracking
  recentLosses: integer("recent_losses").notNull().default(0), // Consecutive losses
  recentWins: integer("recent_wins").notNull().default(0), // Consecutive wins
  biggestWin: decimal("biggest_win", { precision: 10, scale: 2 }).notNull().default("0.00"),
  lastBigWin: timestamp("last_big_win", { withTimezone: true }),
  nearMissCount: integer("near_miss_count").notNull().default(0), // Track near misses for psychology
});

export const gameSessions = pgTable("game_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  sessionId: text("session_id").notNull().unique(),
  startBalance: decimal("start_balance", { precision: 10, scale: 2 }).notNull(),
  currentBalance: decimal("current_balance", { precision: 10, scale: 2 }).notNull(),
  totalBet: decimal("total_bet", { precision: 10, scale: 2 }).notNull().default("0.00"),
  totalWon: decimal("total_won", { precision: 10, scale: 2 }).notNull().default("0.00"),
  spinsCount: integer("spins_count").notNull().default(0),
  currentMultiplier: integer("current_multiplier").notNull().default(1),
  freeSpinsRemaining: integer("free_spins_remaining").notNull().default(0),
  rngSeed: text("rng_seed").notNull(),
  gameState: jsonb("game_state"),
  createdAt: timestamp("created_at", { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).notNull().defaultNow(),
  isActive: boolean("is_active").notNull().default(true),
});

export const spinResults = pgTable("spin_results", {
  id: serial("id").primaryKey(),
  sessionId: text("session_id").notNull(),
  userId: integer("user_id").notNull(),
  betAmount: decimal("bet_amount", { precision: 10, scale: 2 }).notNull(),
  winAmount: decimal("win_amount", { precision: 10, scale: 2 }).notNull().default("0.00"),
  symbols: jsonb("symbols").notNull(), // 5x4 grid of symbols
  winLines: jsonb("win_lines"), // Array of winning combinations
  multiplier: integer("multiplier").notNull().default(1),
  isFreeSpinRound: boolean("is_free_spin_round").notNull().default(false),
  scatterTriggered: boolean("scatter_triggered").notNull().default(false),
  bonusTriggered: boolean("bonus_triggered").notNull().default(false),
  rngValues: jsonb("rng_values").notNull(), // For audit trail
  timestamp: timestamp("timestamp", { withTimezone: true }).notNull().defaultNow(),
});

export const depositRequests = pgTable("deposit_requests", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  bkashNumber: text("bkash_number").notNull(),
  transactionId: text("transaction_id").notNull(),
  status: text("status").notNull().default("pending"), // "pending", "approved", "rejected"
  adminNotes: text("admin_notes"),
  processedBy: integer("processed_by"), // Admin user ID
  createdAt: timestamp("created_at", { withTimezone: true }).notNull().defaultNow(),
  processedAt: timestamp("processed_at", { withTimezone: true }),
});

export const withdrawalRequests = pgTable("withdrawal_requests", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  bkashNumber: text("bkash_number").notNull(),
  status: text("status").notNull().default("pending"), // "pending", "completed", "rejected"
  adminNotes: text("admin_notes"),
  processedBy: integer("processed_by"), // Admin user ID
  createdAt: timestamp("created_at", { withTimezone: true }).notNull().defaultNow(),
  processedAt: timestamp("processed_at", { withTimezone: true }),
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  balance: true,
  totalDeposits: true,
  totalWithdrawals: true,
  totalSpins: true,
  totalWins: true,
  joinDate: true,
  lastLogin: true,
  isActive: true,
}).extend({
  mobile: z.string().regex(/^[0-9]{10,15}$/, "Mobile number must be 10-15 digits"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  name: z.string().min(2, "Name must be at least 2 characters"),
});

export const insertGameSessionSchema = createInsertSchema(gameSessions).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertSpinResultSchema = createInsertSchema(spinResults).omit({
  id: true,
  timestamp: true,
});

export const insertDepositRequestSchema = createInsertSchema(depositRequests).omit({
  id: true,
  status: true,
  adminNotes: true,
  processedBy: true,
  createdAt: true,
  processedAt: true,
}).extend({
  amount: z.number().min(300, "Minimum deposit is 300 BDT"),
  bkashNumber: z.string().regex(/^[0-9]{11}$/, "Bkash number must be 11 digits"),
  transactionId: z.string().min(5, "Transaction ID must be at least 5 characters"),
});

export const insertWithdrawalRequestSchema = createInsertSchema(withdrawalRequests).omit({
  id: true,
  status: true,
  adminNotes: true,
  processedBy: true,
  createdAt: true,
  processedAt: true,
}).extend({
  amount: z.number().min(500, "Minimum withdrawal is 500 BDT"),
  bkashNumber: z.string().regex(/^[0-9]{11}$/, "Bkash number must be 11 digits"),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type GameSession = typeof gameSessions.$inferSelect;
export type InsertGameSession = z.infer<typeof insertGameSessionSchema>;
export type SpinResult = typeof spinResults.$inferSelect;
export type InsertSpinResult = z.infer<typeof insertSpinResultSchema>;
export type DepositRequest = typeof depositRequests.$inferSelect;
export type InsertDepositRequest = z.infer<typeof insertDepositRequestSchema>;
export type WithdrawalRequest = typeof withdrawalRequests.$inferSelect;
export type InsertWithdrawalRequest = z.infer<typeof insertWithdrawalRequestSchema>;
