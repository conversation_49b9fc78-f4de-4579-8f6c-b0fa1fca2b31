import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertUserSchema } from "@shared/schema";
import { z } from "zod";
import { Crown, Sparkles, Star, TrendingUp } from "lucide-react";
import { Redirect } from "wouter";

const loginSchema = insertUserSchema.pick({ mobile: true, password: true });
const registerSchema = insertUserSchema;

type LoginData = z.infer<typeof loginSchema>;
type RegisterData = z.infer<typeof registerSchema>;

export default function AuthPage() {
  const { user, loginMutation, registerMutation } = useAuth();
  const [isLogin, setIsLogin] = useState(true);

  const loginForm = useForm<LoginData>({
    resolver: zodResolver(loginSchema),
    defaultValues: { mobile: "", password: "" },
  });

  const registerForm = useForm<RegisterData>({
    resolver: zodResolver(registerSchema),
    defaultValues: { name: "", mobile: "", password: "" },
  });

  // Redirect if already logged in
  if (user) {
    return <Redirect to="/" />;
  }

  const onLogin = (data: LoginData) => {
    loginMutation.mutate(data);
  };

  const onRegister = (data: RegisterData) => {
    registerMutation.mutate(data);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-deep-navy via-gray-900 to-purple-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 opacity-30">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-casino-purple rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-casino-red rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-96 h-96 bg-casino-gold rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      <div className="relative z-10 grid lg:grid-cols-2 min-h-screen">
        {/* Left Side - Authentication Form */}
        <div className="flex items-center justify-center p-8">
          <Card className="w-full max-w-md glass-card border-casino-gold/30">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-casino-gold to-casino-red rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-3xl font-orbitron font-bold text-casino-gold">
                Vegas Ace Slots
              </CardTitle>
              <p className="text-gray-300">Premium Casino Experience</p>
            </CardHeader>

            <CardContent>
              <Tabs value={isLogin ? "login" : "register"} onValueChange={(value) => setIsLogin(value === "login")}>
                <TabsList className="grid w-full grid-cols-2 mb-6 bg-gray-800/50">
                  <TabsTrigger value="login" className="text-casino-gold data-[state=active]:bg-casino-gold data-[state=active]:text-black">
                    Login
                  </TabsTrigger>
                  <TabsTrigger value="register" className="text-casino-gold data-[state=active]:bg-casino-gold data-[state=active]:text-black">
                    Register
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="login">
                  <form onSubmit={loginForm.handleSubmit(onLogin)} className="space-y-4">
                    <div>
                      <Label htmlFor="login-mobile" className="text-gray-300">Mobile Number</Label>
                      <Input
                        id="login-mobile"
                        type="tel"
                        placeholder="Enter your mobile number"
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-casino-gold"
                        {...loginForm.register("mobile")}
                      />
                      {loginForm.formState.errors.mobile && (
                        <p className="text-casino-red text-sm mt-1">
                          {loginForm.formState.errors.mobile.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="login-password" className="text-gray-300">Password</Label>
                      <Input
                        id="login-password"
                        type="password"
                        placeholder="Enter your password"
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-casino-gold"
                        {...loginForm.register("password")}
                      />
                      {loginForm.formState.errors.password && (
                        <p className="text-casino-red text-sm mt-1">
                          {loginForm.formState.errors.password.message}
                        </p>
                      )}
                    </div>

                    <Button
                      type="submit"
                      disabled={loginMutation.isPending}
                      className="w-full bg-gradient-to-r from-casino-gold to-casino-red hover:from-casino-red hover:to-casino-gold text-black font-bold py-3 transition-all duration-300"
                    >
                      {loginMutation.isPending ? "Signing In..." : "Sign In"}
                    </Button>
                  </form>
                </TabsContent>

                <TabsContent value="register">
                  <form onSubmit={registerForm.handleSubmit(onRegister)} className="space-y-4">
                    <div>
                      <Label htmlFor="register-name" className="text-gray-300">Full Name</Label>
                      <Input
                        id="register-name"
                        type="text"
                        placeholder="Enter your full name"
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-casino-gold"
                        {...registerForm.register("name")}
                      />
                      {registerForm.formState.errors.name && (
                        <p className="text-casino-red text-sm mt-1">
                          {registerForm.formState.errors.name.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="register-mobile" className="text-gray-300">Mobile Number</Label>
                      <Input
                        id="register-mobile"
                        type="tel"
                        placeholder="Enter your mobile number (10-15 digits)"
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-casino-gold"
                        {...registerForm.register("mobile")}
                      />
                      {registerForm.formState.errors.mobile && (
                        <p className="text-casino-red text-sm mt-1">
                          {registerForm.formState.errors.mobile.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="register-password" className="text-gray-300">Password</Label>
                      <Input
                        id="register-password"
                        type="password"
                        placeholder="Create a password"
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-casino-gold"
                        {...registerForm.register("password")}
                      />
                      {registerForm.formState.errors.password && (
                        <p className="text-casino-red text-sm mt-1">
                          {registerForm.formState.errors.password.message}
                        </p>
                      )}
                    </div>

                    <Button
                      type="submit"
                      disabled={registerMutation.isPending}
                      className="w-full bg-gradient-to-r from-casino-purple to-casino-pink hover:from-casino-pink hover:to-casino-purple text-white font-bold py-3 transition-all duration-300"
                    >
                      {registerMutation.isPending ? "Creating Account..." : "Create Account"}
                    </Button>
                  </form>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Right Side - Hero Section */}
        <div className="hidden lg:flex items-center justify-center p-8 relative">
          <div className="text-center max-w-lg">
            <div className="w-32 h-32 bg-gradient-to-r from-casino-gold via-casino-red to-casino-purple rounded-full flex items-center justify-center mx-auto mb-8 animate-pulse">
              <Sparkles className="w-16 h-16 text-white" />
            </div>

            <h1 className="text-5xl font-orbitron font-black text-transparent bg-clip-text bg-gradient-to-r from-casino-gold via-casino-red to-casino-purple mb-6">
              VEGAS ACE SLOTS
            </h1>

            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Experience the thrill of authentic casino gaming with our premium slot machine featuring
              crypto-secure RNG, 1024 ways to win, and massive progressive jackpots.
            </p>

            <div className="grid grid-cols-1 gap-6 mb-8">
              <div className="glass-card p-6 rounded-xl text-left">
                <div className="flex items-center space-x-3 mb-3">
                  <Star className="w-6 h-6 text-casino-gold" />
                  <h3 className="text-lg font-bold text-casino-gold">$1000 Welcome Bonus</h3>
                </div>
                <p className="text-gray-300">Start your casino journey with $1000 in playing credits</p>
              </div>

              <div className="glass-card p-6 rounded-xl text-left">
                <div className="flex items-center space-x-3 mb-3">
                  <TrendingUp className="w-6 h-6 text-casino-purple" />
                  <h3 className="text-lg font-bold text-casino-purple">94.2% RTP</h3>
                </div>
                <p className="text-gray-300">Industry-leading return to player percentage</p>
              </div>

              <div className="glass-card p-6 rounded-xl text-left">
                <div className="flex items-center space-x-3 mb-3">
                  <Crown className="w-6 h-6 text-casino-red" />
                  <h3 className="text-lg font-bold text-casino-red">Crypto-Secure RNG</h3>
                </div>
                <p className="text-gray-300">Provably fair gaming with cryptographic randomness</p>
              </div>
            </div>

            <div className="text-sm text-gray-400">
              <p>🔒 SSL Secured • 🎯 18+ Only • 🎰 Licensed Gaming</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
