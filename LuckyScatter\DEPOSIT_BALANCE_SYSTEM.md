# 💰 Deposit-Based Balance System

## Overview
This system controls user luck based on their **deposit vs withdrawal ratio**. Users who withdraw too much get reduced luck and frequent small wins to keep them hooked, while good depositors get better chances and bigger wins.

## 🎯 Core Concept

**The game tracks each user's financial behavior:**
- **Total Deposits**: All approved deposits
- **Total Withdrawals**: All completed withdrawals  
- **Balance Ratio**: `withdrawals ÷ deposits`
- **Net Balance**: `deposits - withdrawals`

**Based on this ratio, the game adjusts their luck:**

| Withdrawal Ratio | User Type | Luck Level | Strategy |
|------------------|-----------|------------|----------|
| 0-30% | **Safe Depositor** | HIGH LUCK | Normal gameplay + occasional big wins |
| 30-60% | **Moderate User** | NORMAL LUCK | Standard balanced gameplay |
| 60-80% | **High Withdrawer** | REDUCED LUCK | Frequent small wins to hook them |
| 80%+ | **Critical Risk** | VERY LOW LUCK | Rare medium wins to create hope |

## 🎮 How It Works

### 1. **Safe Depositors (0-30% withdrawal ratio)** 💎
- **40% win chance** (vs 25% normal)
- **Up to 5x bet multipliers**
- **8% scatter chance**
- **Occasional big wins** (10% chance for 8x multiplier)
- **Reason**: Reward loyal depositors to encourage more deposits

### 2. **Moderate Users (30-60% withdrawal ratio)** ⚖️
- **25% win chance** (normal)
- **Up to 3x bet multipliers**
- **5% scatter chance**
- **Standard gameplay**
- **Reason**: Balanced users get fair treatment

### 3. **High Withdrawers (60-80% withdrawal ratio)** 🎣
- **30% win chance BUT smaller wins**
- **Max 1.5x bet multipliers**
- **3% scatter chance**
- **Hook Strategy**: Frequent tiny wins (0.8x-1.5x bet)
- **Reason**: Keep them playing with small wins instead of big losses

### 4. **Critical Risk Users (80%+ withdrawal ratio)** 🚨
- **15% win chance**
- **Max 1.2x bet multipliers**
- **2% scatter chance**
- **Rare Hope Strategy**: 5% chance for 3x medium win
- **Reason**: Very controlled luck to prevent further losses

## 🔧 Technical Implementation

### Deposit Balance Engine (`server/deposit-balance-engine.ts`)

#### User Balance Tracking:
```typescript
interface UserBalance {
  userId: number;
  totalDeposits: number;
  totalWithdrawals: number;
  netBalance: number;
  balanceRatio: number; // withdrawals / deposits
  recentSpins: number;
  recentWins: number;
  bigWinsCount: number;
  riskLevel: 'safe' | 'moderate' | 'high' | 'critical';
}
```

#### Luck Adjustment:
```typescript
interface LuckAdjustment {
  shouldWin: boolean;
  winType: 'tiny' | 'small' | 'medium' | 'big' | 'jackpot';
  winChance: number; // 0-1
  maxWinMultiplier: number;
  scatterChance: number;
  hookStrategy: 'normal' | 'frequent_small' | 'rare_medium' | 'very_rare_big';
}
```

### Integration with Game Logic

#### 1. **Spin Processing** (`server/storage.ts`):
```typescript
// Get luck adjustment based on deposits vs withdrawals
const luckAdjustment = await depositBalanceEngine.calculateLuckAdjustment(userId, bet);

// Determine win based on deposit balance
const shouldWin = luckAdjustment.shouldWin || rng.next() < luckAdjustment.winChance;
const shouldBonus = rng.next() < luckAdjustment.scatterChance;
```

#### 2. **Dynamic Reel Selection**:
- **Safe users**: Normal or high variance reels
- **High withdrawers**: Low variance reels with frequent small wins
- **Critical users**: Bonus-heavy reels for rare hope wins

#### 3. **Win Multiplier Application**:
```typescript
// Apply deposit-based multiplier
if (shouldWin && totalWin > 0) {
  totalWin *= luckAdjustment.maxWinMultiplier;
}
```

## 🎣 Hook Strategies

### 1. **Frequent Small Wins** (High Withdrawers)
- **60% chance** for tiny wins when they should win
- **Win amounts**: 0.8x to 1.5x bet
- **Psychology**: Keeps them playing with constant small rewards
- **Goal**: Prevent big losses while slowly reducing their balance

### 2. **Rare Medium Wins** (Critical Users)
- **5% chance** for medium wins (3x bet)
- **95% of time**: Tiny wins or losses
- **Psychology**: Creates hope and prevents complete abandonment
- **Goal**: Keep them engaged with occasional "luck"

### 3. **Normal Gameplay** (Safe Depositors)
- **Standard win patterns** with bonus big wins
- **10% chance** for 8x multiplier wins
- **Psychology**: Reward loyalty and encourage more deposits
- **Goal**: Maximize lifetime value of good customers

## 📊 Real-Time Adjustments

### Recent Activity Modifiers:
- **High recent win rate (>40%)**: Reduce luck by 30%
- **Low recent win rate (<10%)**: Small boost (except critical users)
- **Big bet users**: Reduced treatment if high withdrawal ratio
- **Small bet users**: Slightly better odds to encourage play

### Bet Amount Considerations:
```typescript
// For high withdrawal users
if (betAmount <= 5) {
  adjustment.winChance *= 1.1; // +10% for small bets
} else {
  adjustment.winChance *= 0.8; // -20% for large bets
}
```

## 🔄 Auto-Refresh System

### Deposit/Withdrawal Triggers:
- **After deposit approval**: `depositBalanceEngine.refreshUserBalance(userId)`
- **After withdrawal completion**: `depositBalanceEngine.refreshUserBalance(userId)`
- **Real-time updates**: Balance ratios recalculated immediately

### Memory Management:
- **Cleanup interval**: Every 2 hours
- **Cache duration**: Active users kept in memory
- **Database sync**: Real-time for deposits/withdrawals

## 📈 Expected Business Impact

### Player Retention:
- **High withdrawers**: Kept engaged with frequent small wins
- **Critical users**: Prevented from complete abandonment
- **Good depositors**: Rewarded to encourage more deposits

### Revenue Optimization:
- **Controlled payouts**: High withdrawers get smaller wins
- **Loyalty rewards**: Good depositors get better treatment
- **Smart balance**: Prevents excessive losses while maintaining engagement

### Psychological Effects:
- **Addiction mechanics**: Frequent small rewards for problem users
- **Hope creation**: Rare big wins for critical users
- **Positive reinforcement**: Reward good financial behavior

## 🎯 Key Features

### 1. **Totally Luck-Based Feel**
- Game still feels random and natural
- No obvious patterns or manipulation
- Smooth win/loss distribution

### 2. **Deposit-Driven Logic**
- Everything based on real financial behavior
- Automatic adjustment without manual intervention
- Fair treatment based on user value

### 3. **Hook Mechanisms**
- Frequent small wins prevent rage quitting
- Rare medium wins create hope
- Balanced approach maintains engagement

### 4. **Business Intelligence**
- Real-time user classification
- Automatic risk assessment
- Revenue optimization through behavior analysis

## 🔧 Configuration

### Adjustable Thresholds:
```typescript
SAFE_RATIO = 0.3      // 30% withdrawal ratio
MODERATE_RATIO = 0.6  // 60% withdrawal ratio  
HIGH_RATIO = 0.8      // 80% withdrawal ratio
CRITICAL_RATIO = 1.0  // 100% withdrawal ratio
```

### Win Chances:
```typescript
Safe: 40% win chance, 5x max multiplier
Moderate: 25% win chance, 3x max multiplier
High: 30% win chance, 1.5x max multiplier
Critical: 15% win chance, 1.2x max multiplier
```

## 🎮 User Experience

From the player's perspective:
- **Game feels natural** and luck-based
- **No obvious manipulation** or patterns
- **Appropriate rewards** based on their behavior
- **Engaging gameplay** regardless of deposit history
- **Fair treatment** that matches their value to the business

This system ensures that users who deposit more get better treatment, while users who withdraw too much are kept engaged through psychological hook mechanisms, all while maintaining the appearance of a fair, luck-based game.
