/**
 * 🎯 NATURAL LUCK ENGINE - Self-Learning Emotional Slot System
 * Creates a "living" game that adapts to player behavior and emotions
 */

import { User } from "@shared/schema";

export interface PlayerState {
  userId: number;
  spinsSinceLastWin: number;
  netLossesThisSession: number;
  timePlayedMinutes: number;
  sessionStartBalance: number;
  currentBalance: number;
  consecutiveLosses: number;
  lastMegaJackpotSpin: number;
  totalSpins: number;
  lastScatterSpin: number;
  maxBetLossStreak: number;
  bankrollRecoveryMode: boolean;
  progressiveMultiplierStep: number; // 0=none, 1=1x, 2=2x, 3=3x, 4=5x
}

export interface LuckFactors {
  recentLossFactor: number;    // 0-1 based on recent losses
  droughtFactor: number;       // 0-1 based on spins since win
  sessionLossFactor: number;   // 0-1 based on session net loss
  emotionalFactor: number;     // 0-1 based on frustration indicators
  timeFactor: number;          // 0-1 based on time played
}

export class NaturalLuckEngine {
  private playerStates = new Map<number, PlayerState>();
  
  // 🎯 Tuning parameters for LuckScore calculation
  private readonly ALPHA = 0.4;  // Weight for recent loss factor
  private readonly BETA = 0.3;   // Weight for drought factor
  private readonly GAMMA = 0.2;  // Weight for session loss factor
  private readonly DELTA = 0.1;  // Weight for emotional/time factors
  
  // 🎯 Thresholds and limits
  private readonly TARGET_DROUGHT = 25;     // Target spins between wins
  private readonly MIN_RTP = 0.85;          // 85% minimum RTP
  private readonly MAX_RTP = 0.95;          // 95% maximum RTP
  private readonly MEGA_JACKPOT_COOLDOWN = 100; // Minimum spins between mega jackpots
  
  /**
   * 🎯 Get or create player state
   */
  public getPlayerState(userId: number): PlayerState {
    if (!this.playerStates.has(userId)) {
      this.playerStates.set(userId, {
        userId,
        spinsSinceLastWin: 0,
        netLossesThisSession: 0,
        timePlayedMinutes: 0,
        sessionStartBalance: 0,
        currentBalance: 0,
        consecutiveLosses: 0,
        lastMegaJackpotSpin: 0,
        totalSpins: 0,
        lastScatterSpin: 0,
        maxBetLossStreak: 0,
        bankrollRecoveryMode: false,
        progressiveMultiplierStep: 0,
      });
    }
    return this.playerStates.get(userId)!;
  }

  /**
   * 🧠 Calculate dynamic LuckScore using exponential smoothing
   */
  public calculateLuckScore(userId: number, betAmount: number): number {
    const state = this.getPlayerState(userId);
    const factors = this.calculateLuckFactors(state, betAmount);
    
    // 🎯 Core LuckScore formula with exponential smoothing
    const luckScore = 
      this.ALPHA * factors.recentLossFactor +
      this.BETA * factors.droughtFactor +
      this.GAMMA * factors.sessionLossFactor +
      this.DELTA * (factors.emotionalFactor + factors.timeFactor) / 2;
    
    // Clamp to [0, 1] range
    return Math.max(0, Math.min(1, luckScore));
  }

  /**
   * 🎯 Calculate individual luck factors
   */
  private calculateLuckFactors(state: PlayerState, betAmount: number): LuckFactors {
    // 📉 Recent Loss Factor (exponential growth with consecutive losses)
    const recentLossFactor = Math.min(1, 
      Math.pow(state.consecutiveLosses / 10, 1.5) // Exponential curve
    );
    
    // 🌵 Drought Factor (spins since last win vs target)
    const droughtFactor = Math.min(1, 
      state.spinsSinceLastWin / this.TARGET_DROUGHT
    );
    
    // 💸 Session Loss Factor (net losses as percentage of starting balance)
    const sessionLossPercent = state.sessionStartBalance > 0 
      ? state.netLossesThisSession / state.sessionStartBalance 
      : 0;
    const sessionLossFactor = Math.min(1, sessionLossPercent * 2); // 50% loss = max factor
    
    // 😤 Emotional Factor (frustration indicators)
    let emotionalFactor = 0;
    if (state.maxBetLossStreak >= 5) emotionalFactor += 0.3; // Lost 5+ max bets
    if (state.consecutiveLosses >= 15) emotionalFactor += 0.4; // Long loss streak
    if (state.currentBalance < state.sessionStartBalance * 0.3) emotionalFactor += 0.3; // Low balance
    
    // ⏰ Time Factor (longer sessions need more engagement)
    const timeFactor = Math.min(0.3, state.timePlayedMinutes / 60); // Max 30% boost after 1 hour
    
    return {
      recentLossFactor,
      droughtFactor,
      sessionLossFactor,
      emotionalFactor: Math.min(1, emotionalFactor),
      timeFactor,
    };
  }

  /**
   * 🎰 Calculate dynamic win chance based on LuckScore
   */
  public calculateWinChance(userId: number, betAmount: number, baseWinChance: number): number {
    const luckScore = this.calculateLuckScore(userId, betAmount);
    const state = this.getPlayerState(userId);
    
    // 🎯 Base adjustment based on LuckScore
    let adjustedWinChance = baseWinChance;
    
    if (luckScore > 0.6) {
      // High luck = boost win chance
      adjustedWinChance *= (1 + (luckScore - 0.6) * 1.5); // Up to 60% boost
    } else if (luckScore < 0.3) {
      // Low luck = slightly reduce win chance
      adjustedWinChance *= (0.8 + luckScore * 0.67); // Down to 80% of base
    }
    
    // 🎯 Bet-scaled adjustments
    const maxBet = 100; // Adjust based on your max bet
    const betRatio = betAmount / maxBet;
    
    if (betRatio <= 0.2) {
      // Low bets: +10% win chance, but lower max payouts
      adjustedWinChance *= 1.1;
    } else if (betRatio > 0.8) {
      // High bets: -10% win chance, but +15% bonus chance
      adjustedWinChance *= 0.9;
    }
    
    // 🎯 Progressive multiplier mode
    if (state.progressiveMultiplierStep > 0) {
      adjustedWinChance *= 1.5; // Guarantee wins during progression
    }
    
    // 🎯 Ensure RTP compliance
    return this.clampForRTP(adjustedWinChance, state);
  }

  /**
   * 💥 Calculate scatter chance with emotional timing
   */
  public calculateScatterChance(userId: number, betAmount: number): number {
    const luckScore = this.calculateLuckScore(userId, betAmount);
    const state = this.getPlayerState(userId);
    
    let baseScatterChance = 0.03; // 3% base chance
    
    // 🍀 LuckScore boost
    if (luckScore > 0.6) {
      const spinsSinceScatter = state.totalSpins - state.lastScatterSpin;
      
      // Force scatter if drought is too long
      if (spinsSinceScatter > 40) {
        return 1.0; // 100% chance - force scatter
      }
      
      // Otherwise boost based on luck
      baseScatterChance += luckScore * 0.05; // Up to 8% total
    }
    
    // 🎯 High bet bonus
    const maxBet = 100;
    if (betAmount > maxBet * 0.8) {
      baseScatterChance *= 1.15; // 15% boost for high bets
    }
    
    // 🚫 Mega jackpot cooldown
    const spinsSinceMega = state.totalSpins - state.lastMegaJackpotSpin;
    if (spinsSinceMega < this.MEGA_JACKPOT_COOLDOWN) {
      baseScatterChance *= 0.5; // Reduce chance if recent mega
    }
    
    return Math.min(0.15, baseScatterChance); // Cap at 15%
  }

  /**
   * 🎯 Determine scatter count (3, 4, or 5)
   */
  public determineScatterCount(userId: number, betAmount: number): number {
    const luckScore = this.calculateLuckScore(userId, betAmount);
    const state = this.getPlayerState(userId);
    
    // Base probabilities
    let prob3 = 0.70; // 70% chance for 3 scatters
    let prob4 = 0.25; // 25% chance for 4 scatters  
    let prob5 = 0.05; // 5% chance for 5 scatters (mega jackpot)
    
    // 🍀 Luck adjustments
    if (luckScore > 0.8) {
      prob3 = 0.50;
      prob4 = 0.35;
      prob5 = 0.15; // Higher chance for mega when very unlucky
    }
    
    // 🚫 Mega jackpot cooldown
    const spinsSinceMega = state.totalSpins - state.lastMegaJackpotSpin;
    if (spinsSinceMega < this.MEGA_JACKPOT_COOLDOWN) {
      prob5 = 0; // No mega jackpot during cooldown
      prob4 += 0.05;
    }
    
    const rand = Math.random();
    if (rand < prob5) return 5;
    if (rand < prob5 + prob4) return 4;
    return 3;
  }

  /**
   * 🎯 Calculate win multiplier with Super Ace progression
   */
  public calculateWinMultiplier(userId: number, betAmount: number, isBonus: boolean): number {
    const state = this.getPlayerState(userId);
    const luckScore = this.calculateLuckScore(userId, betAmount);
    
    // 🎯 Progressive multiplier system (like Super Ace)
    if (state.progressiveMultiplierStep > 0) {
      const multipliers = [1, 1, 2, 3, 5]; // Index 0 unused
      return multipliers[state.progressiveMultiplierStep];
    }
    
    // 🎯 Standard multiplier selection
    const multipliers = [1, 2, 3, 5];
    let weights = [40, 35, 20, 5]; // Base weights
    
    // 🍀 Luck-based weight adjustment
    if (luckScore > 0.7) {
      weights = [20, 30, 35, 15]; // Better multipliers when unlucky
    } else if (luckScore < 0.3) {
      weights = [60, 25, 12, 3]; // Worse multipliers when lucky
    }
    
    // 🎰 Bonus round enhancement
    if (isBonus) {
      weights = [10, 25, 40, 25]; // Much better multipliers in bonus
    }
    
    return this.weightedRandomSelect(multipliers, weights);
  }

  /**
   * 🎯 Update player state after spin
   */
  public updatePlayerState(
    userId: number, 
    won: boolean, 
    winAmount: number, 
    betAmount: number,
    scatterTriggered: boolean,
    megaJackpot: boolean
  ): void {
    const state = this.getPlayerState(userId);
    
    state.totalSpins++;
    
    if (won) {
      state.spinsSinceLastWin = 0;
      state.consecutiveLosses = 0;
      state.maxBetLossStreak = 0;
      state.currentBalance += winAmount - betAmount;
      
      // Check for bankroll recovery
      if (state.currentBalance > state.sessionStartBalance) {
        state.bankrollRecoveryMode = true;
        state.progressiveMultiplierStep = 1; // Start progression
      }
    } else {
      state.spinsSinceLastWin++;
      state.consecutiveLosses++;
      state.netLossesThisSession += betAmount;
      state.currentBalance -= betAmount;
      
      // Track max bet losses
      const maxBet = 100; // Adjust based on your max bet
      if (betAmount >= maxBet * 0.8) {
        state.maxBetLossStreak++;
      }
    }
    
    // Update scatter tracking
    if (scatterTriggered) {
      state.lastScatterSpin = state.totalSpins;
      
      if (megaJackpot) {
        state.lastMegaJackpotSpin = state.totalSpins;
      }
    }
    
    // Progress multiplier system
    if (state.progressiveMultiplierStep > 0) {
      if (won) {
        state.progressiveMultiplierStep++;
        if (state.progressiveMultiplierStep > 4) {
          // Reset after 5x multiplier
          state.progressiveMultiplierStep = 0;
          state.bankrollRecoveryMode = false;
        }
      }
    }
    
    // Update session start balance if first spin
    if (state.totalSpins === 1) {
      state.sessionStartBalance = state.currentBalance + betAmount;
    }
  }

  /**
   * 🎯 Weighted random selection
   */
  private weightedRandomSelect<T>(items: T[], weights: number[]): T {
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;
    
    for (let i = 0; i < items.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return items[i];
      }
    }
    
    return items[items.length - 1];
  }

  /**
   * 🎯 Clamp win chance to maintain RTP compliance
   */
  private clampForRTP(winChance: number, state: PlayerState): number {
    // Simple RTP check - in production, use rolling window calculation
    const estimatedRTP = this.estimateCurrentRTP(state);
    
    if (estimatedRTP < this.MIN_RTP) {
      return Math.min(1, winChance * 1.2); // Boost if RTP too low
    } else if (estimatedRTP > this.MAX_RTP) {
      return Math.max(0.05, winChance * 0.8); // Reduce if RTP too high
    }
    
    return winChance;
  }

  /**
   * 🎯 Estimate current RTP (simplified)
   */
  private estimateCurrentRTP(state: PlayerState): number {
    if (state.totalSpins < 10) return 0.9; // Default for new players
    
    const totalWagered = state.sessionStartBalance - state.currentBalance + state.netLossesThisSession;
    const totalReturned = totalWagered - state.netLossesThisSession;
    
    return totalWagered > 0 ? totalReturned / totalWagered : 0.9;
  }

  /**
   * 🎯 Get player analytics for debugging
   */
  public getPlayerAnalytics(userId: number): {
    luckScore: number;
    factors: LuckFactors;
    state: PlayerState;
    estimatedRTP: number;
  } {
    const state = this.getPlayerState(userId);
    const luckScore = this.calculateLuckScore(userId, 10); // Use default bet for calculation
    const factors = this.calculateLuckFactors(state, 10);
    const estimatedRTP = this.estimateCurrentRTP(state);
    
    return {
      luckScore,
      factors,
      state,
      estimatedRTP,
    };
  }
}

// Export singleton instance
export const naturalLuckEngine = new NaturalLuckEngine();
