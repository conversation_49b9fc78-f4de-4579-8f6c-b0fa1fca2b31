/**
 * 🎰 REEL ANIMATION STYLES - 1000+ Pattern Effects
 * CSS animations for all different reel drop patterns
 */

/* Base spinning animation */
.reel-spinning {
  animation: spin 0.1s linear infinite;
}

@keyframes spin {
  0% { transform: translateY(0); }
  100% { transform: translateY(-10px); }
}

/* Glow effects */
.reel-glow {
  animation: reelGlow 1s ease-in-out;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
}

@keyframes reelGlow {
  0%, 100% { box-shadow: 0 0 10px rgba(255, 215, 0, 0.4); }
  50% { box-shadow: 0 0 30px rgba(255, 215, 0, 1); }
}

/* Pulse effects */
.reel-pulse {
  animation: reelPulse 1.5s ease-in-out;
}

@keyframes reelPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  25% { transform: scale(1.05); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 0.6; }
  75% { transform: scale(1.05); opacity: 0.8; }
}

/* Flash effects */
.reel-flash {
  animation: reelFlash 0.8s ease-in-out;
}

@keyframes reelFlash {
  0%, 100% { background-color: transparent; }
  25% { background-color: rgba(255, 255, 255, 0.3); }
  50% { background-color: rgba(255, 215, 0, 0.5); }
  75% { background-color: rgba(255, 255, 255, 0.3); }
}

/* Explosion effects */
.reel-explosion {
  animation: reelExplosion 2s ease-out;
}

@keyframes reelExplosion {
  0% { transform: scale(1); }
  10% { transform: scale(1.2) rotate(5deg); }
  20% { transform: scale(0.9) rotate(-3deg); }
  30% { transform: scale(1.3) rotate(2deg); }
  50% { transform: scale(1.1) rotate(-1deg); }
  100% { transform: scale(1) rotate(0deg); }
}

/* Dramatic effects */
.reel-dramatic {
  animation: reelDramatic 2.5s ease-in-out;
}

@keyframes reelDramatic {
  0% { transform: translateY(0) scale(1); }
  20% { transform: translateY(-20px) scale(1.1); }
  40% { transform: translateY(10px) scale(0.95); }
  60% { transform: translateY(-15px) scale(1.05); }
  80% { transform: translateY(5px) scale(0.98); }
  100% { transform: translateY(0) scale(1); }
}

/* Wave effects */
.reel-wave {
  animation: reelWave 1.2s ease-in-out;
}

@keyframes reelWave {
  0%, 100% { transform: translateX(0) rotateY(0deg); }
  25% { transform: translateX(10px) rotateY(15deg); }
  50% { transform: translateX(-5px) rotateY(-10deg); }
  75% { transform: translateX(8px) rotateY(8deg); }
}

/* Bounce effects */
.reel-bounce {
  animation: reelBounce 1s ease-out;
}

@keyframes reelBounce {
  0% { transform: translateY(0); }
  20% { transform: translateY(-30px); }
  40% { transform: translateY(10px); }
  60% { transform: translateY(-15px); }
  80% { transform: translateY(5px); }
  100% { transform: translateY(0); }
}

/* Spiral effects */
.reel-spiral {
  animation: reelSpiral 1.5s ease-in-out;
}

@keyframes reelSpiral {
  0% { transform: rotate(0deg) scale(1); }
  25% { transform: rotate(90deg) scale(1.1); }
  50% { transform: rotate(180deg) scale(0.9); }
  75% { transform: rotate(270deg) scale(1.05); }
  100% { transform: rotate(360deg) scale(1); }
}

/* Zigzag effects */
.reel-zigzag {
  animation: reelZigzag 1.3s ease-in-out;
}

@keyframes reelZigzag {
  0%, 100% { transform: translateX(0); }
  12.5% { transform: translateX(15px); }
  25% { transform: translateX(-10px); }
  37.5% { transform: translateX(12px); }
  50% { transform: translateX(-8px); }
  62.5% { transform: translateX(10px); }
  75% { transform: translateX(-6px); }
  87.5% { transform: translateX(5px); }
}

/* Cascade effects */
.reel-cascade {
  animation: reelCascade 1.1s ease-out;
}

@keyframes reelCascade {
  0% { transform: translateY(-50px) opacity(0); }
  20% { transform: translateY(-30px) opacity(0.3); }
  40% { transform: translateY(-15px) opacity(0.6); }
  60% { transform: translateY(-5px) opacity(0.8); }
  80% { transform: translateY(2px) opacity(0.9); }
  100% { transform: translateY(0) opacity(1); }
}

/* Intense effects */
.reel-intense {
  animation: reelIntense 2s ease-in-out;
}

@keyframes reelIntense {
  0%, 100% { 
    transform: scale(1); 
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
  }
  10% { 
    transform: scale(1.1); 
    box-shadow: 0 0 20px rgba(255, 100, 0, 0.6);
  }
  20% { 
    transform: scale(0.95); 
    box-shadow: 0 0 15px rgba(255, 150, 0, 0.5);
  }
  30% { 
    transform: scale(1.15); 
    box-shadow: 0 0 25px rgba(255, 200, 0, 0.7);
  }
  50% { 
    transform: scale(1.05); 
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
  }
  70% { 
    transform: scale(1.08); 
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.6);
  }
}

/* Climax effects */
.reel-climax {
  animation: reelClimax 3s ease-in-out;
}

@keyframes reelClimax {
  0% { 
    transform: scale(1) rotate(0deg); 
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  }
  10% { 
    transform: scale(1.2) rotate(5deg); 
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
  }
  20% { 
    transform: scale(0.9) rotate(-3deg); 
    box-shadow: 0 0 40px rgba(255, 100, 255, 0.6);
  }
  30% { 
    transform: scale(1.3) rotate(8deg); 
    box-shadow: 0 0 50px rgba(100, 255, 255, 0.8);
  }
  50% { 
    transform: scale(1.1) rotate(-2deg); 
    box-shadow: 0 0 60px rgba(255, 255, 100, 1);
  }
  70% { 
    transform: scale(1.15) rotate(3deg); 
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.9);
  }
  90% { 
    transform: scale(1.05) rotate(-1deg); 
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  }
  100% { 
    transform: scale(1) rotate(0deg); 
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  }
}

/* Anticipation effects */
.reel-anticipation {
  animation: reelAnticipation 1s ease-in-out infinite;
}

@keyframes reelAnticipation {
  0%, 100% { 
    transform: scale(1); 
    border-color: rgba(255, 215, 0, 0.5);
  }
  50% { 
    transform: scale(1.02); 
    border-color: rgba(255, 215, 0, 1);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
  }
}

/* Winning symbol effects */
.winning-symbol {
  animation: winPulse 1s ease-in-out 3;
  border-color: #ffd700 !important;
  background-color: rgba(255, 215, 0, 0.2) !important;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
}

@keyframes winPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Speed variations */
.reel-speed-slow {
  animation-duration: 0.2s !important;
}

.reel-speed-fast {
  animation-duration: 0.05s !important;
}

.reel-speed-normal {
  animation-duration: 0.1s !important;
}

/* 🎰 NEW: Enhanced Animation Effects for Varied Drop Patterns */

/* Intense effect for dramatic moments */
.reel-intense {
  animation: reelIntense 1.2s ease-in-out;
}

@keyframes reelIntense {
  0% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
  }
  25% {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 0 25px rgba(255, 0, 0, 0.8);
  }
  50% {
    transform: scale(0.95) rotate(-3deg);
    box-shadow: 0 0 35px rgba(255, 100, 0, 1);
  }
  75% {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.9);
  }
  100% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
  }
}

/* Climax effect for final reel */
.reel-climax {
  animation: reelClimax 1.5s ease-out;
}

@keyframes reelClimax {
  0% {
    transform: scale(1);
    filter: brightness(1);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  }
  20% {
    transform: scale(1.2);
    filter: brightness(1.3);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.7);
  }
  40% {
    transform: scale(0.9);
    filter: brightness(1.1);
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.9);
  }
  60% {
    transform: scale(1.1);
    filter: brightness(1.4);
    box-shadow: 0 0 50px rgba(255, 215, 0, 1);
  }
  80% {
    transform: scale(0.95);
    filter: brightness(1.2);
    box-shadow: 0 0 35px rgba(255, 215, 0, 0.8);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  }
}

/* Rubber band effect */
.reel-rubber {
  animation: reelRubber 1s ease-in-out;
}

@keyframes reelRubber {
  0% { transform: scale(1, 1); }
  30% { transform: scale(1.25, 0.75); }
  40% { transform: scale(0.75, 1.25); }
  50% { transform: scale(1.15, 0.85); }
  65% { transform: scale(0.95, 1.05); }
  75% { transform: scale(1.05, 0.95); }
  100% { transform: scale(1, 1); }
}

/* Shake effect for tension */
.reel-shake {
  animation: reelShake 0.8s ease-in-out;
}

@keyframes reelShake {
  0%, 100% { transform: translateX(0); }
  10% { transform: translateX(-10px); }
  20% { transform: translateX(10px); }
  30% { transform: translateX(-8px); }
  40% { transform: translateX(8px); }
  50% { transform: translateX(-6px); }
  60% { transform: translateX(6px); }
  70% { transform: translateX(-4px); }
  80% { transform: translateX(4px); }
  90% { transform: translateX(-2px); }
}

/* Flip effect */
.reel-flip {
  animation: reelFlip 1s ease-in-out;
}

@keyframes reelFlip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(180deg); }
  100% { transform: rotateY(360deg); }
}

/* Wobble effect */
.reel-wobble {
  animation: reelWobble 1s ease-in-out;
}

@keyframes reelWobble {
  0% { transform: rotate(0deg); }
  15% { transform: rotate(-5deg); }
  30% { transform: rotate(3deg); }
  45% { transform: rotate(-3deg); }
  60% { transform: rotate(2deg); }
  75% { transform: rotate(-1deg); }
  100% { transform: rotate(0deg); }
}

.reel-speed-ultra {
  animation-duration: 0.03s !important;
}

/* Pattern-specific combinations */
.pattern-sequential .reel-column:nth-child(1) { animation-delay: 0s; }
.pattern-sequential .reel-column:nth-child(2) { animation-delay: 0.2s; }
.pattern-sequential .reel-column:nth-child(3) { animation-delay: 0.4s; }
.pattern-sequential .reel-column:nth-child(4) { animation-delay: 0.6s; }
.pattern-sequential .reel-column:nth-child(5) { animation-delay: 0.8s; }

.pattern-reverse .reel-column:nth-child(1) { animation-delay: 0.8s; }
.pattern-reverse .reel-column:nth-child(2) { animation-delay: 0.6s; }
.pattern-reverse .reel-column:nth-child(3) { animation-delay: 0.4s; }
.pattern-reverse .reel-column:nth-child(4) { animation-delay: 0.2s; }
.pattern-reverse .reel-column:nth-child(5) { animation-delay: 0s; }

.pattern-center-out .reel-column:nth-child(3) { animation-delay: 0s; }
.pattern-center-out .reel-column:nth-child(2) { animation-delay: 0.2s; }
.pattern-center-out .reel-column:nth-child(4) { animation-delay: 0.2s; }
.pattern-center-out .reel-column:nth-child(1) { animation-delay: 0.4s; }
.pattern-center-out .reel-column:nth-child(5) { animation-delay: 0.4s; }

/* Responsive adjustments */
@media (max-width: 768px) {
  .reel-glow { box-shadow: 0 0 10px rgba(255, 215, 0, 0.6); }
  .reel-explosion { animation-duration: 1.5s; }
  .reel-dramatic { animation-duration: 2s; }
  .reel-climax { animation-duration: 2.5s; }
}

/* Accessibility - reduced motion */
@media (prefers-reduced-motion: reduce) {
  .reel-spinning,
  .reel-glow,
  .reel-pulse,
  .reel-flash,
  .reel-explosion,
  .reel-dramatic,
  .reel-wave,
  .reel-bounce,
  .reel-spiral,
  .reel-zigzag,
  .reel-cascade,
  .reel-intense,
  .reel-climax,
  .reel-anticipation {
    animation: none !important;
    transform: none !important;
  }
}
