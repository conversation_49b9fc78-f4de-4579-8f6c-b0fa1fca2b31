/**
 * 🎰 ENHANCED REEL ANIMATOR - 1000+ Unique Drop Patterns
 * Uses advanced pattern system for varied, engaging reel animations
 */

import { AdvancedReelPatterns, ReelDropPattern, PatternConfig } from './advanced-reel-patterns';

export interface SpinResult {
  grid: string[][];
  winAmount: number;
  winLines: any[];
  scatterCount: number;
  hasBonus: boolean;
  multiplier: number;
  freeSpinsTriggered: number;
}

export interface AnimationConfig {
  userLevel: 'new' | 'regular' | 'vip' | 'whale';
  consecutiveLosses: number;
  luckScore: number;
  anticipationMode: boolean;
}

export class EnhancedReelAnimator {
  private patternGenerator: AdvancedReelPatterns;
  private currentPattern: ReelDropPattern | null = null;
  private animationStates: Map<number, any> = new Map();
  private symbols = ['9', '10', 'J', 'Q', 'K', 'A', 'SCATTER', 'WILD'];

  constructor() {
    this.patternGenerator = new AdvancedReelPatterns();
    console.log('🎰 Enhanced Reel Animator initialized with', this.patternGenerator.getPatternStats());
  }

  /**
   * 🎯 Main animation method with pattern selection
   */
  public async animateReels(
    finalGrid: string[][],
    spinResult: SpinResult,
    config: AnimationConfig
  ): Promise<void> {
    console.log('🎰 Starting enhanced reel animation...');

    // Select optimal pattern based on game state
    const patternConfig: PatternConfig = {
      winType: this.determineWinType(spinResult.winAmount),
      scatterCount: spinResult.scatterCount,
      hasBonus: spinResult.hasBonus,
      userLevel: config.userLevel,
      consecutiveLosses: config.consecutiveLosses,
      luckScore: config.luckScore
    };

    this.currentPattern = this.patternGenerator.selectPattern(patternConfig);
    console.log(`🎰 Selected pattern: ${this.currentPattern.name} (${this.currentPattern.type})`);

    // Get reel elements
    const reelElements = Array.from(document.querySelectorAll('.reel-column'));
    if (reelElements.length === 0) {
      console.warn('🎰 No reel elements found');
      return;
    }

    // Start spinning phase
    await this.startSpinningPhase(reelElements);

    // Execute pattern-based stopping sequence
    await this.executePatternSequence(reelElements, finalGrid, spinResult);

    // Final cleanup
    this.cleanupAnimation(reelElements);
  }

  /**
   * 🎯 Start spinning phase for all reels
   */
  private async startSpinningPhase(reelElements: Element[]): Promise<void> {
    console.log('🎰 Starting spinning phase...');

    // Add spinning classes and start symbol cycling
    reelElements.forEach((reel, index) => {
      reel.classList.add('reel-spinning');
      this.startSymbolCycling(reel as HTMLElement, index);
    });

    // Let reels spin for a base duration
    await new Promise(resolve => setTimeout(resolve, 800));
  }

  /**
   * 🎯 Execute the selected pattern sequence
   */
  private async executePatternSequence(
    reelElements: Element[],
    finalGrid: string[][],
    spinResult: SpinResult
  ): Promise<void> {
    if (!this.currentPattern) return;

    console.log(`🎰 Executing pattern: ${this.currentPattern.name}`);

    // Create stopping promises for each reel based on pattern
    const stopPromises = reelElements.map((reel, index) => 
      this.stopReelWithPattern(
        reel as HTMLElement,
        index,
        finalGrid[index],
        this.currentPattern!,
        spinResult
      )
    );

    // Wait for all reels to stop
    await Promise.all(stopPromises);
  }

  /**
   * 🎯 Stop individual reel according to pattern
   */
  private async stopReelWithPattern(
    reel: HTMLElement,
    reelIndex: number,
    finalSymbols: string[],
    pattern: ReelDropPattern,
    spinResult: SpinResult
  ): Promise<void> {
    const timing = pattern.timings[reelIndex];
    const speed = pattern.speeds[reelIndex];
    const effect = pattern.effects[reelIndex];

    // Wait for pattern timing
    await new Promise(resolve => setTimeout(resolve, timing));

    // Apply special effects based on pattern
    this.applyReelEffect(reel, effect, spinResult);

    // Stop reel with pattern-specific speed
    await this.stopReelWithSpeed(reel, reelIndex, finalSymbols, speed);

    // Handle anticipation if needed
    if (pattern.anticipation && this.shouldShowAnticipation(reelIndex, spinResult)) {
      await this.showAnticipationEffect(reel, reelIndex);
    }
  }

  /**
   * 🎯 Apply visual effects to reel
   */
  private applyReelEffect(reel: HTMLElement, effect: string, spinResult: SpinResult): void {
    switch (effect) {
      case 'glow':
        reel.classList.add('reel-glow');
        setTimeout(() => reel.classList.remove('reel-glow'), 1000);
        break;
      case 'pulse':
        reel.classList.add('reel-pulse');
        setTimeout(() => reel.classList.remove('reel-pulse'), 1500);
        break;
      case 'flash':
        reel.classList.add('reel-flash');
        setTimeout(() => reel.classList.remove('reel-flash'), 800);
        break;
      case 'explosion':
        reel.classList.add('reel-explosion');
        setTimeout(() => reel.classList.remove('reel-explosion'), 2000);
        break;
      case 'dramatic':
        reel.classList.add('reel-dramatic');
        setTimeout(() => reel.classList.remove('reel-dramatic'), 2500);
        break;
      case 'wave':
        reel.classList.add('reel-wave');
        setTimeout(() => reel.classList.remove('reel-wave'), 1200);
        break;
      case 'bounce':
        reel.classList.add('reel-bounce');
        setTimeout(() => reel.classList.remove('reel-bounce'), 1000);
        break;
      case 'spiral':
        reel.classList.add('reel-spiral');
        setTimeout(() => reel.classList.remove('reel-spiral'), 1500);
        break;
      case 'zigzag':
        reel.classList.add('reel-zigzag');
        setTimeout(() => reel.classList.remove('reel-zigzag'), 1300);
        break;
      case 'cascade':
        reel.classList.add('reel-cascade');
        setTimeout(() => reel.classList.remove('reel-cascade'), 1100);
        break;
      case 'intense':
        reel.classList.add('reel-intense');
        setTimeout(() => reel.classList.remove('reel-intense'), 2000);
        break;
      case 'climax':
        reel.classList.add('reel-climax');
        setTimeout(() => reel.classList.remove('reel-climax'), 3000);
        break;
    }
  }

  /**
   * 🎯 Stop reel with specific speed
   */
  private async stopReelWithSpeed(
    reel: HTMLElement,
    reelIndex: number,
    finalSymbols: string[],
    speed: number
  ): Promise<void> {
    // Clear existing animation state
    this.animationStates.delete(reelIndex);

    // Apply speed-based deceleration
    const decelerationTime = Math.max(200, 500 / speed);
    
    // Gradual slowdown
    reel.style.animationDuration = `${0.2 / speed}s`;
    
    await new Promise(resolve => setTimeout(resolve, decelerationTime));

    // Final stop
    reel.classList.remove('reel-spinning');
    this.setFinalSymbols(reel, finalSymbols);
  }

  /**
   * 🎯 Start symbol cycling during spin
   */
  private startSymbolCycling(reel: HTMLElement, reelIndex: number): void {
    const symbols = reel.querySelectorAll('.symbol-container');
    
    const cycleInterval = setInterval(() => {
      if (!this.animationStates.has(reelIndex)) {
        clearInterval(cycleInterval);
        return;
      }

      symbols.forEach((symbol) => {
        const randomSymbol = this.symbols[Math.floor(Math.random() * this.symbols.length)];
        this.updateSymbolDisplay(symbol as HTMLElement, randomSymbol);
      });
    }, 80);

    this.animationStates.set(reelIndex, { cycleInterval });
  }

  /**
   * 🎯 Update symbol display
   */
  private updateSymbolDisplay(element: HTMLElement, symbol: string): void {
    const symbolMap: Record<string, string> = {
      '9': '9️⃣',
      '10': '🔟',
      'J': '👨‍💼',
      'Q': '👸',
      'K': '🤴',
      'A': '🎯',
      'SCATTER': '⭐',
      'WILD': '🃏'
    };

    element.innerHTML = `
      <div class="text-4xl">${symbolMap[symbol] || symbol}</div>
      <div class="text-xs text-casino-gold/70 mt-1">${symbol}</div>
    `;
  }

  /**
   * 🎯 Set final symbols
   */
  private setFinalSymbols(reel: HTMLElement, finalSymbols: string[]): void {
    const symbolElements = reel.querySelectorAll('.symbol-container');
    finalSymbols.forEach((symbol, index) => {
      if (symbolElements[index]) {
        this.updateSymbolDisplay(symbolElements[index] as HTMLElement, symbol);
      }
    });
  }

  /**
   * 🎯 Determine win type from amount
   */
  private determineWinType(winAmount: number): 'none' | 'small' | 'medium' | 'big' | 'mega' | 'jackpot' {
    if (winAmount === 0) return 'none';
    if (winAmount < 10) return 'small';
    if (winAmount < 50) return 'medium';
    if (winAmount < 200) return 'big';
    if (winAmount < 1000) return 'mega';
    return 'jackpot';
  }

  /**
   * 🎯 Check if anticipation should be shown
   */
  private shouldShowAnticipation(reelIndex: number, spinResult: SpinResult): boolean {
    return spinResult.scatterCount >= 2 && reelIndex >= 2;
  }

  /**
   * 🎯 Show anticipation effect
   */
  private async showAnticipationEffect(reel: HTMLElement, reelIndex: number): Promise<void> {
    reel.classList.add('reel-anticipation');
    await new Promise(resolve => setTimeout(resolve, 1000));
    reel.classList.remove('reel-anticipation');
  }

  /**
   * 🎯 Cleanup animation
   */
  private cleanupAnimation(reelElements: Element[]): void {
    reelElements.forEach((reel, index) => {
      reel.classList.remove('reel-spinning');
      this.animationStates.delete(index);
    });
    console.log('🎰 Animation cleanup completed');
  }
}
