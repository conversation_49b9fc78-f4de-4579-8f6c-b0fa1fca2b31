/**
 * 🎰 ADVANCED REEL PATTERNS - 1000+ Unique Drop Patterns
 * Creates varied, unpredictable reel stopping sequences for maximum engagement
 */

export interface ReelDropPattern {
  id: number;
  name: string;
  type: 'sequential' | 'random' | 'cascade' | 'wave' | 'bounce' | 'spiral' | 'zigzag' | 'dramatic';
  timings: number[]; // Delay for each reel (0-4)
  speeds: number[]; // Speed multiplier for each reel
  effects: string[]; // Visual effects for each reel
  anticipation: boolean; // Whether to use anticipation effects
  description: string;
}

export interface PatternConfig {
  winType: 'none' | 'small' | 'medium' | 'big' | 'mega' | 'jackpot';
  scatterCount: number;
  hasBonus: boolean;
  userLevel: 'new' | 'regular' | 'vip' | 'whale';
  consecutiveLosses: number;
  luckScore: number;
}

export class AdvancedReelPatterns {
  private patterns: ReelDropPattern[] = [];
  private patternHistory: number[] = [];
  private maxHistorySize = 50;

  constructor() {
    this.generateAllPatterns();
  }

  /**
   * 🎯 Generate 1000+ unique reel drop patterns
   */
  private generateAllPatterns(): void {
    let patternId = 1;

    // 1. Sequential Patterns (100 variations)
    for (let i = 0; i < 100; i++) {
      this.patterns.push(this.createSequentialPattern(patternId++, i));
    }

    // 2. Random Patterns (150 variations)
    for (let i = 0; i < 150; i++) {
      this.patterns.push(this.createRandomPattern(patternId++, i));
    }

    // 3. Cascade Patterns (120 variations)
    for (let i = 0; i < 120; i++) {
      this.patterns.push(this.createCascadePattern(patternId++, i));
    }

    // 4. Wave Patterns (100 variations)
    for (let i = 0; i < 100; i++) {
      this.patterns.push(this.createWavePattern(patternId++, i));
    }

    // 5. Bounce Patterns (80 variations)
    for (let i = 0; i < 80; i++) {
      this.patterns.push(this.createBouncePattern(patternId++, i));
    }

    // 6. Spiral Patterns (90 variations)
    for (let i = 0; i < 90; i++) {
      this.patterns.push(this.createSpiralPattern(patternId++, i));
    }

    // 7. Zigzag Patterns (70 variations)
    for (let i = 0; i < 70; i++) {
      this.patterns.push(this.createZigzagPattern(patternId++, i));
    }

    // 8. Dramatic Patterns (100 variations)
    for (let i = 0; i < 100; i++) {
      this.patterns.push(this.createDramaticPattern(patternId++, i));
    }

    // 9. Special Win Patterns (90 variations)
    for (let i = 0; i < 90; i++) {
      this.patterns.push(this.createSpecialWinPattern(patternId++, i));
    }

    // 10. Anticipation Patterns (100 variations)
    for (let i = 0; i < 100; i++) {
      this.patterns.push(this.createAnticipationPattern(patternId++, i));
    }

    console.log(`🎰 Generated ${this.patterns.length} unique reel drop patterns!`);
  }

  /**
   * 🎯 Select optimal pattern based on game state
   */
  public selectPattern(config: PatternConfig): ReelDropPattern {
    let candidatePatterns: ReelDropPattern[] = [];

    // Filter patterns based on win type
    switch (config.winType) {
      case 'none':
        candidatePatterns = this.patterns.filter(p => 
          p.type === 'sequential' || p.type === 'random' || p.type === 'wave'
        );
        break;
      case 'small':
        candidatePatterns = this.patterns.filter(p => 
          p.type === 'cascade' || p.type === 'bounce'
        );
        break;
      case 'medium':
        candidatePatterns = this.patterns.filter(p => 
          p.type === 'spiral' || p.type === 'zigzag'
        );
        break;
      case 'big':
        candidatePatterns = this.patterns.filter(p => 
          p.type === 'dramatic' && p.anticipation
        );
        break;
      case 'mega':
      case 'jackpot':
        candidatePatterns = this.patterns.filter(p => 
          p.type === 'dramatic' && p.name.includes('Mega')
        );
        break;
    }

    // Add scatter-specific patterns
    if (config.scatterCount >= 2) {
      const scatterPatterns = this.patterns.filter(p => p.anticipation);
      candidatePatterns = [...candidatePatterns, ...scatterPatterns];
    }

    // Add retention patterns for losing streaks
    if (config.consecutiveLosses >= 5) {
      const retentionPatterns = this.patterns.filter(p => 
        p.name.includes('Retention') || p.name.includes('Hope')
      );
      candidatePatterns = [...candidatePatterns, ...retentionPatterns];
    }

    // Ensure we have candidates
    if (candidatePatterns.length === 0) {
      candidatePatterns = this.patterns.filter(p => p.type === 'sequential');
    }

    // Remove recently used patterns to avoid repetition
    const availablePatterns = candidatePatterns.filter(p => 
      !this.patternHistory.includes(p.id)
    );

    const finalCandidates = availablePatterns.length > 0 ? availablePatterns : candidatePatterns;
    
    // Select random pattern from candidates
    const selectedPattern = finalCandidates[Math.floor(Math.random() * finalCandidates.length)];
    
    // Update history
    this.updatePatternHistory(selectedPattern.id);
    
    return selectedPattern;
  }

  /**
   * 🎯 Create sequential patterns (left to right, right to left, etc.)
   */
  private createSequentialPattern(id: number, variation: number): ReelDropPattern {
    const baseDelay = 200 + (variation * 10);
    const patterns = [
      // Left to right
      [0, baseDelay, baseDelay * 2, baseDelay * 3, baseDelay * 4],
      // Right to left
      [baseDelay * 4, baseDelay * 3, baseDelay * 2, baseDelay, 0],
      // Center out
      [baseDelay * 2, baseDelay, 0, baseDelay, baseDelay * 2],
      // Outside in
      [0, baseDelay * 3, baseDelay * 4, baseDelay * 3, baseDelay],
    ];

    const patternIndex = variation % patterns.length;
    const speedVariation = 0.8 + (variation % 5) * 0.1;

    return {
      id,
      name: `Sequential ${variation + 1}`,
      type: 'sequential',
      timings: patterns[patternIndex],
      speeds: [speedVariation, speedVariation, speedVariation, speedVariation, speedVariation],
      effects: ['none', 'none', 'none', 'none', 'none'],
      anticipation: false,
      description: `Sequential drop pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Create random patterns with controlled chaos
   */
  private createRandomPattern(id: number, variation: number): ReelDropPattern {
    const timings: number[] = [];
    const speeds: number[] = [];
    const effects: string[] = [];

    for (let i = 0; i < 5; i++) {
      timings.push(Math.random() * 1000 + (variation * 5));
      speeds.push(0.7 + Math.random() * 0.6);
      effects.push(Math.random() > 0.8 ? 'glow' : 'none');
    }

    return {
      id,
      name: `Random Chaos ${variation + 1}`,
      type: 'random',
      timings,
      speeds,
      effects,
      anticipation: Math.random() > 0.7,
      description: `Unpredictable random pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Create cascade patterns (waterfall effect)
   */
  private createCascadePattern(id: number, variation: number): ReelDropPattern {
    const baseDelay = 150 + (variation * 8);
    const cascade = variation % 3;
    
    let timings: number[];
    switch (cascade) {
      case 0: // Standard cascade
        timings = [0, baseDelay, baseDelay * 2, baseDelay * 3, baseDelay * 4];
        break;
      case 1: // Reverse cascade
        timings = [baseDelay * 4, baseDelay * 3, baseDelay * 2, baseDelay, 0];
        break;
      default: // Double cascade
        timings = [0, baseDelay * 2, baseDelay, baseDelay * 3, baseDelay * 1.5];
    }

    return {
      id,
      name: `Cascade ${variation + 1}`,
      type: 'cascade',
      timings,
      speeds: [1.2, 1.1, 1.0, 0.9, 0.8],
      effects: ['cascade', 'cascade', 'cascade', 'cascade', 'cascade'],
      anticipation: variation % 4 === 0,
      description: `Cascading waterfall pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Update pattern history to avoid repetition
   */
  private updatePatternHistory(patternId: number): void {
    this.patternHistory.push(patternId);
    if (this.patternHistory.length > this.maxHistorySize) {
      this.patternHistory.shift();
    }
  }

  /**
   * 🎯 Create wave patterns (smooth flowing motion)
   */
  private createWavePattern(id: number, variation: number): ReelDropPattern {
    const amplitude = 300 + (variation * 10);
    const frequency = 0.5 + (variation % 10) * 0.1;
    const timings: number[] = [];

    for (let i = 0; i < 5; i++) {
      const wave = Math.sin(i * frequency) * amplitude;
      timings.push(Math.abs(wave) + (variation * 5));
    }

    return {
      id,
      name: `Wave Motion ${variation + 1}`,
      type: 'wave',
      timings,
      speeds: [1.0, 1.1, 1.2, 1.1, 1.0],
      effects: ['wave', 'wave', 'wave', 'wave', 'wave'],
      anticipation: variation % 5 === 0,
      description: `Smooth wave motion pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Create bounce patterns (elastic motion)
   */
  private createBouncePattern(id: number, variation: number): ReelDropPattern {
    const bounceHeight = 200 + (variation * 15);
    const bounceCount = 2 + (variation % 3);
    const timings: number[] = [];

    for (let i = 0; i < 5; i++) {
      const bounce = Math.abs(Math.sin(i * bounceCount)) * bounceHeight;
      timings.push(bounce + (variation * 8));
    }

    return {
      id,
      name: `Bounce ${variation + 1}`,
      type: 'bounce',
      timings,
      speeds: [0.8, 1.2, 0.9, 1.1, 1.0],
      effects: ['bounce', 'bounce', 'bounce', 'bounce', 'bounce'],
      anticipation: variation % 6 === 0,
      description: `Elastic bounce pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Create spiral patterns (rotating motion)
   */
  private createSpiralPattern(id: number, variation: number): ReelDropPattern {
    const spiralSpeed = 0.3 + (variation % 8) * 0.1;
    const radius = 250 + (variation * 12);
    const timings: number[] = [];

    for (let i = 0; i < 5; i++) {
      const angle = i * spiralSpeed * Math.PI;
      const spiral = Math.abs(Math.cos(angle)) * radius;
      timings.push(spiral + (variation * 6));
    }

    return {
      id,
      name: `Spiral ${variation + 1}`,
      type: 'spiral',
      timings,
      speeds: [1.1, 0.9, 1.2, 0.8, 1.0],
      effects: ['spiral', 'spiral', 'spiral', 'spiral', 'spiral'],
      anticipation: variation % 7 === 0,
      description: `Rotating spiral pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Create zigzag patterns (alternating motion)
   */
  private createZigzagPattern(id: number, variation: number): ReelDropPattern {
    const zigzagHeight = 180 + (variation * 20);
    const timings: number[] = [];

    for (let i = 0; i < 5; i++) {
      const zigzag = (i % 2 === 0 ? 1 : -1) * zigzagHeight;
      timings.push(Math.abs(zigzag) + (variation * 7));
    }

    return {
      id,
      name: `Zigzag ${variation + 1}`,
      type: 'zigzag',
      timings,
      speeds: [1.0, 0.8, 1.3, 0.7, 1.1],
      effects: ['zigzag', 'zigzag', 'zigzag', 'zigzag', 'zigzag'],
      anticipation: variation % 8 === 0,
      description: `Alternating zigzag pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Create dramatic patterns (for big wins)
   */
  private createDramaticPattern(id: number, variation: number): ReelDropPattern {
    const dramaticPause = 500 + (variation * 25);
    const buildupTime = 200 + (variation * 10);

    const patterns = [
      [0, buildupTime, dramaticPause, buildupTime * 2, dramaticPause * 1.5],
      [dramaticPause, buildupTime, 0, dramaticPause * 0.8, buildupTime * 1.5],
      [buildupTime, dramaticPause * 1.2, buildupTime * 0.5, 0, dramaticPause],
    ];

    const patternIndex = variation % patterns.length;
    const isMega = variation % 10 === 0;

    return {
      id,
      name: isMega ? `Mega Drama ${variation + 1}` : `Dramatic ${variation + 1}`,
      type: 'dramatic',
      timings: patterns[patternIndex],
      speeds: [0.6, 0.8, 1.0, 1.2, 1.4],
      effects: ['dramatic', 'dramatic', 'dramatic', 'dramatic', 'dramatic'],
      anticipation: true,
      description: `${isMega ? 'Mega ' : ''}Dramatic tension pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Create special win patterns (for different win types)
   */
  private createSpecialWinPattern(id: number, variation: number): ReelDropPattern {
    const winTypes = ['small', 'medium', 'big', 'mega'];
    const winType = winTypes[variation % winTypes.length];
    const baseDelay = 150 + (variation * 12);

    let timings: number[];
    let speeds: number[];
    let effects: string[];

    switch (winType) {
      case 'small':
        timings = [0, baseDelay * 0.5, baseDelay, baseDelay * 1.5, baseDelay * 2];
        speeds = [1.0, 1.0, 1.0, 1.0, 1.0];
        effects = ['none', 'none', 'glow', 'none', 'none'];
        break;
      case 'medium':
        timings = [0, baseDelay, baseDelay * 2, baseDelay * 1.5, baseDelay * 3];
        speeds = [1.1, 0.9, 1.2, 0.8, 1.0];
        effects = ['glow', 'glow', 'pulse', 'glow', 'glow'];
        break;
      case 'big':
        timings = [baseDelay, 0, baseDelay * 2, baseDelay * 0.5, baseDelay * 3];
        speeds = [0.8, 1.3, 0.7, 1.4, 0.6];
        effects = ['pulse', 'pulse', 'flash', 'pulse', 'pulse'];
        break;
      default: // mega
        timings = [baseDelay * 2, baseDelay, 0, baseDelay * 3, baseDelay * 4];
        speeds = [0.5, 1.5, 2.0, 0.4, 0.3];
        effects = ['flash', 'flash', 'explosion', 'flash', 'flash'];
    }

    return {
      id,
      name: `${winType.charAt(0).toUpperCase() + winType.slice(1)} Win ${variation + 1}`,
      type: 'dramatic',
      timings,
      speeds,
      effects,
      anticipation: winType === 'big' || winType === 'mega',
      description: `Special ${winType} win pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Create anticipation patterns (for scatter builds)
   */
  private createAnticipationPattern(id: number, variation: number): ReelDropPattern {
    const anticipationDelay = 300 + (variation * 20);
    const buildupDelay = 150 + (variation * 8);

    const patterns = [
      // Two scatters, building to third
      [0, buildupDelay, anticipationDelay * 2, buildupDelay * 2, anticipationDelay * 3],
      // Scattered anticipation
      [buildupDelay, 0, anticipationDelay * 1.5, anticipationDelay * 2.5, buildupDelay * 3],
      // Progressive build
      [0, buildupDelay * 0.5, buildupDelay, anticipationDelay, anticipationDelay * 2],
    ];

    const patternIndex = variation % patterns.length;
    const isRetention = variation % 15 === 0; // Special retention patterns

    return {
      id,
      name: isRetention ? `Retention Hope ${variation + 1}` : `Anticipation ${variation + 1}`,
      type: 'dramatic',
      timings: patterns[patternIndex],
      speeds: [1.0, 1.0, 0.7, 0.5, 0.3], // Slow down for anticipation
      effects: ['none', 'glow', 'pulse', 'intense', 'climax'],
      anticipation: true,
      description: `${isRetention ? 'Player retention ' : ''}Anticipation building pattern ${variation + 1}`
    };
  }

  /**
   * 🎯 Get pattern statistics
   */
  public getPatternStats(): { total: number; types: Record<string, number> } {
    const types: Record<string, number> = {};
    this.patterns.forEach(pattern => {
      types[pattern.type] = (types[pattern.type] || 0) + 1;
    });

    return {
      total: this.patterns.length,
      types
    };
  }
}
