/**
 * AI Feedback Display Component
 * Shows psychological effects and AI decision feedback to enhance player engagement
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Brain, Target, Zap, TrendingUp, AlertTriangle } from 'lucide-react';

interface AIDecision {
  reason: string;
  shouldWin: boolean;
  winMultiplier: number;
  outcomeType: 'big_win' | 'small_win' | 'near_miss' | 'ldw' | 'loss';
  psychologicalEffect: 'celebration' | 'near_miss_excitement' | 'anticipation' | 'neutral';
  bonusForced: boolean;
}

interface UserStats {
  totalSpins: number;
  rtp: number;
  currentStreak: number;
  playStyle: 'casual' | 'aggressive' | 'whale';
  retentionRisk: 'low' | 'medium' | 'high';
  avgBetSize: number;
  nearMissCount: number;
  ldwCount: number;
  spinsSinceLastBonus: number;
}

interface AIFeedbackDisplayProps {
  aiDecision?: AIDecision;
  userStats?: UserStats;
  winAmount: number;
  betAmount: number;
  show: boolean;
}

export const AIFeedbackDisplay: React.FC<AIFeedbackDisplayProps> = ({
  aiDecision,
  userStats,
  winAmount,
  betAmount,
  show
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [psychEffect, setPsychEffect] = useState<string>('');

  useEffect(() => {
    if (aiDecision?.psychologicalEffect && show) {
      setPsychEffect(aiDecision.psychologicalEffect);
      
      // Auto-hide after 3 seconds
      const timer = setTimeout(() => {
        setPsychEffect('');
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [aiDecision, show]);

  if (!show || !aiDecision || !userStats) return null;

  const getOutcomeIcon = (outcomeType: string) => {
    switch (outcomeType) {
      case 'big_win': return '🎉';
      case 'small_win': return '✨';
      case 'near_miss': return '🎯';
      case 'ldw': return '🎭';
      case 'loss': return '❌';
      default: return '🎰';
    }
  };

  const getOutcomeColor = (outcomeType: string) => {
    switch (outcomeType) {
      case 'big_win': return 'text-casino-gold';
      case 'small_win': return 'text-green-400';
      case 'near_miss': return 'text-yellow-400';
      case 'ldw': return 'text-purple-400';
      case 'loss': return 'text-gray-400';
      default: return 'text-white';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getPlayStyleIcon = (style: string) => {
    switch (style) {
      case 'whale': return '🐋';
      case 'aggressive': return '⚡';
      case 'casual': return '🎮';
      default: return '👤';
    }
  };

  return (
    <div className="space-y-4">
      {/* Psychological Effect Display */}
      {psychEffect && (
        <Card className={`glass-card border-casino-gold/50 transition-all duration-500 ${
          psychEffect === 'celebration' ? 'animate-pulse border-green-400' :
          psychEffect === 'near_miss_excitement' ? 'animate-bounce border-yellow-400' :
          psychEffect === 'anticipation' ? 'animate-pulse border-purple-400' :
          'border-casino-gold/30'
        }`}>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center space-x-2">
              <Brain className="w-5 h-5 text-casino-gold" />
              <span className="text-sm font-medium">
                {psychEffect === 'celebration' && '🎉 Celebration Mode!'}
                {psychEffect === 'near_miss_excitement' && '🎯 So Close!'}
                {psychEffect === 'anticipation' && '⏳ Building Anticipation...'}
                {psychEffect === 'neutral' && '🎰 Standard Play'}
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Decision Summary */}
      <Card className="glass-card border-casino-gold/30">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Brain className="w-4 h-4 text-casino-gold" />
              <span className="text-sm font-medium text-casino-gold">AI Analysis</span>
            </div>
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-xs text-gray-400 hover:text-casino-gold transition-colors"
            >
              {showDetails ? 'Hide' : 'Show'} Details
            </button>
          </div>

          <div className="grid grid-cols-2 gap-4 text-xs">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Outcome:</span>
                <div className="flex items-center space-x-1">
                  <span>{getOutcomeIcon(aiDecision.outcomeType)}</span>
                  <span className={getOutcomeColor(aiDecision.outcomeType)}>
                    {aiDecision.outcomeType.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Player Type:</span>
                <div className="flex items-center space-x-1">
                  <span>{getPlayStyleIcon(userStats.playStyle)}</span>
                  <span className="text-casino-gold capitalize">{userStats.playStyle}</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Streak:</span>
                <span className={userStats.currentStreak >= 0 ? 'text-green-400' : 'text-red-400'}>
                  {userStats.currentStreak >= 0 ? '+' : ''}{userStats.currentStreak}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Risk Level:</span>
                <span className={getRiskColor(userStats.retentionRisk)}>
                  {userStats.retentionRisk.toUpperCase()}
                </span>
              </div>
            </div>
          </div>

          {showDetails && (
            <div className="mt-4 pt-4 border-t border-casino-gold/20 space-y-3">
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-400">RTP:</span>
                    <span className="text-casino-gold">{(userStats.rtp * 100).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Total Spins:</span>
                    <span className="text-white">{userStats.totalSpins}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Avg Bet:</span>
                    <span className="text-white">${userStats.avgBetSize.toFixed(2)}</span>
                  </div>
                </div>
                
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Near Misses:</span>
                    <span className="text-yellow-400">{userStats.nearMissCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">LDWs:</span>
                    <span className="text-purple-400">{userStats.ldwCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Since Bonus:</span>
                    <span className="text-white">{userStats.spinsSinceLastBonus}</span>
                  </div>
                </div>
              </div>

              {/* AI Reason */}
              <div className="bg-black/20 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <Target className="w-4 h-4 text-casino-gold mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-xs text-gray-300 leading-relaxed">
                      {aiDecision.reason}
                    </p>
                    {aiDecision.bonusForced && (
                      <Badge variant="outline" className="mt-2 text-xs border-casino-gold text-casino-gold">
                        Bonus Forced
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* LDW Explanation */}
              {aiDecision.outcomeType === 'ldw' && winAmount < betAmount && (
                <div className="bg-purple-900/20 border border-purple-400/30 rounded-lg p-3">
                  <div className="flex items-start space-x-2">
                    <Zap className="w-4 h-4 text-purple-400 mt-0.5" />
                    <div>
                      <p className="text-xs text-purple-300 font-medium">Loss-Disguised-as-Win</p>
                      <p className="text-xs text-purple-200 mt-1">
                        Won ${winAmount.toFixed(2)} on ${betAmount.toFixed(2)} bet (net loss: -${(betAmount - winAmount).toFixed(2)})
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AIFeedbackDisplay;
