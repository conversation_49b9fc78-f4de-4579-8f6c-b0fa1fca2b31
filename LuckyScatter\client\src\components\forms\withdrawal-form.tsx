import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, CreditCard, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { z } from "zod";

const withdrawalSchema = z.object({
  amount: z.number().min(500, "Minimum withdrawal is 500 BDT"),
  bkashNumber: z.string().regex(/^[0-9]{11}$/, "Bkash number must be 11 digits"),
});

interface WithdrawalFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function WithdrawalForm({ onSuccess, onCancel }: WithdrawalFormProps) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    amount: "",
    bkashNumber: "",
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const userBalance = parseFloat(user?.balance || "0");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      
      const amount = parseFloat(formData.amount);
      
      // Check if user has sufficient balance
      if (amount > userBalance) {
        throw new Error("Insufficient balance for withdrawal");
      }
      
      // Validate form data
      const validatedData = withdrawalSchema.parse({
        amount,
        bkashNumber: formData.bkashNumber,
      });

      // Submit withdrawal request
      const response = await fetch('/api/withdrawals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validatedData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit withdrawal request');
      }

      toast({
        title: "Withdrawal Request Submitted",
        description: `৳${amount.toFixed(2)} has been deducted from your balance and is pending admin processing.`,
      });

      // Reset form
      setFormData({
        amount: "",
        bkashNumber: "",
      });

      onSuccess?.();
    } catch (error) {
      console.error('Withdrawal error:', error);
      
      if (error instanceof z.ZodError) {
        toast({
          title: "Validation Error",
          description: error.errors[0].message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Withdrawal Failed",
          description: error instanceof Error ? error.message : "Failed to submit withdrawal request",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Card className="glass-card border-casino-red/30 max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-casino-red flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2" />
          Withdraw Funds
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Current Balance */}
          <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
            <div className="text-center">
              <p className="text-green-400 text-sm font-medium">Current Balance</p>
              <p className="text-white font-bold text-2xl">৳{userBalance.toFixed(2)}</p>
            </div>
          </div>

          {/* Warning */}
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-red-400 mt-0.5" />
              <div className="text-sm">
                <p className="text-red-400 font-medium mb-2">Important Notice:</p>
                <ul className="text-gray-300 space-y-1 text-xs">
                  <li>• Amount will be deducted immediately</li>
                  <li>• Processing takes 24-48 hours</li>
                  <li>• Minimum withdrawal: 500 BDT</li>
                  <li>• Ensure your Bkash number is correct</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount" className="text-gray-300">
              Withdrawal Amount (BDT) *
            </Label>
            <Input
              id="amount"
              type="number"
              placeholder="Minimum 500 BDT"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              className="bg-black/20 border-gray-600 text-white placeholder-gray-400"
              min="500"
              max={userBalance}
              step="1"
              required
            />
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">Minimum: 500 BDT</span>
              <span className="text-gray-400">Available: ৳{userBalance.toFixed(2)}</span>
            </div>
          </div>

          {/* Quick Amount Buttons */}
          <div className="grid grid-cols-3 gap-2">
            {[
              { label: "25%", value: Math.floor(userBalance * 0.25) },
              { label: "50%", value: Math.floor(userBalance * 0.5) },
              { label: "Max", value: Math.floor(userBalance) }
            ].map((option) => (
              <Button
                key={option.label}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleInputChange('amount', option.value.toString())}
                className="border-casino-gold/30 text-casino-gold hover:bg-casino-gold/10"
                disabled={option.value < 500}
              >
                {option.label}
              </Button>
            ))}
          </div>

          {/* Bkash Number */}
          <div className="space-y-2">
            <Label htmlFor="bkashNumber" className="text-gray-300">
              Your Bkash Number *
            </Label>
            <Input
              id="bkashNumber"
              type="tel"
              placeholder="01XXXXXXXXX"
              value={formData.bkashNumber}
              onChange={(e) => handleInputChange('bkashNumber', e.target.value.replace(/\D/g, ''))}
              className="bg-black/20 border-gray-600 text-white placeholder-gray-400"
              maxLength={11}
              required
            />
            <p className="text-xs text-gray-400">Money will be sent to this Bkash number</p>
          </div>

          {/* Processing Info */}
          <div className="bg-casino-purple/10 border border-casino-purple/30 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <CreditCard className="w-4 h-4 text-casino-purple" />
              <span className="text-casino-purple font-medium text-sm">Processing Time:</span>
            </div>
            <p className="text-gray-300 text-xs">
              Withdrawals are processed manually by our admin team within 24-48 hours during business hours.
            </p>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
                disabled={loading}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              className="flex-1 bg-casino-red text-white hover:bg-casino-red/90 font-bold"
              disabled={loading || userBalance < 500}
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                "Submit Withdrawal"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
