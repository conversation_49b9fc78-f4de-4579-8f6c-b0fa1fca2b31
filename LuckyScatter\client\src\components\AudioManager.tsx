/**
 * 🎵 CASINO AUDIO MANAGER
 * Handles background music and sound effects for the slot game
 */

import React, { useEffect, useRef, useState } from 'react';
import { Volume2, VolumeX, Music } from 'lucide-react';

interface AudioManagerProps {
  isGameActive?: boolean;
  onSpinSound?: () => void;
  onWinSound?: () => void;
  onSoundEffectsRef?: (ref: { playSpinSound: () => void; playWinSound: () => void }) => void;
}

export const AudioManager: React.FC<AudioManagerProps> = ({
  isGameActive = false,
  onSpinSound,
  onWinSound,
  onSoundEffectsRef
}) => {
  const backgroundMusicRef = useRef<HTMLAudioElement | null>(null);
  const [isMusicEnabled, setIsMusicEnabled] = useState(false);
  const [isMusicLoaded, setIsMusicLoaded] = useState(false);
  const [volume, setVolume] = useState(0.3); // 30% volume by default
  const [isPlaying, setIsPlaying] = useState(false);

  // Initialize background music
  useEffect(() => {
    const audio = new Audio('/assets/casino-music.mp3');
    audio.loop = true;
    audio.volume = volume;
    audio.preload = 'metadata';

    // Handle audio loading
    audio.addEventListener('loadedmetadata', () => {
      setIsMusicLoaded(true);
      console.log('🎵 Casino music loaded successfully');
    });

    audio.addEventListener('canplaythrough', () => {
      setIsMusicLoaded(true);
      console.log('🎵 Casino music ready to play');
    });

    audio.addEventListener('error', (e) => {
      console.error('🎵 Error loading casino music:', e);
      console.log('🎵 Please add your casino music file to /assets/casino-music.mp3');
      setIsMusicLoaded(true); // Set to true to remove loading spinner
    });

    audio.addEventListener('play', () => {
      setIsPlaying(true);
    });

    audio.addEventListener('pause', () => {
      setIsPlaying(false);
    });

    backgroundMusicRef.current = audio;

    // Try to load the audio file
    audio.load();

    return () => {
      if (backgroundMusicRef.current) {
        backgroundMusicRef.current.pause();
        backgroundMusicRef.current = null;
      }
    };
  }, []);

  // Update volume when changed
  useEffect(() => {
    if (backgroundMusicRef.current) {
      backgroundMusicRef.current.volume = volume;
    }
  }, [volume]);

  // Note: Auto-play removed due to browser restrictions
  // Users must manually click to start music

  // Expose sound effect functions to parent component
  useEffect(() => {
    if (onSoundEffectsRef) {
      onSoundEffectsRef({
        playSpinSound,
        playWinSound
      });
    }
  }, [onSoundEffectsRef]);

  const playBackgroundMusic = async () => {
    if (backgroundMusicRef.current && isMusicLoaded) {
      try {
        await backgroundMusicRef.current.play();
        console.log('🎵 Casino jazz music started');
      } catch (error) {
        console.error('🎵 Error playing music:', error);
        // Browser might require user interaction first
      }
    }
  };

  const pauseBackgroundMusic = () => {
    if (backgroundMusicRef.current) {
      backgroundMusicRef.current.pause();
      console.log('🎵 Casino music paused');
    }
  };

  const toggleMusic = async () => {
    if (!isMusicLoaded) {
      console.log('🎵 Music not loaded yet');
      return;
    }

    if (isMusicEnabled) {
      // Turn off music
      pauseBackgroundMusic();
      setIsMusicEnabled(false);
    } else {
      // Turn on music
      setIsMusicEnabled(true);
      await playBackgroundMusic();
    }
  };

  const adjustVolume = (newVolume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, newVolume));
    setVolume(clampedVolume);
  };

  // Spin sound effect
  const playSpinSound = () => {
    if (onSpinSound) {
      onSpinSound();
    }
    // Create a simple spin sound using Web Audio API
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
      oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 0.5);

      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.5);

      console.log('🎰 Spin sound effect played');
    } catch (error) {
      console.log('🎰 Spin sound effect (audio not available)');
    }
  };

  // Win sound effect
  const playWinSound = () => {
    if (onWinSound) {
      onWinSound();
    }
    // Create a simple win sound using Web Audio API
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // Happy win sound - ascending notes
      oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(500, audioContext.currentTime + 0.1);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.2);
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.3);

      gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.4);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.4);

      console.log('🎉 Win sound effect played');
    } catch (error) {
      console.log('🎉 Win sound effect (audio not available)');
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 flex items-center gap-2">
      {/* Music Control Panel */}
      <div className="bg-black/80 backdrop-blur-sm rounded-lg p-2 flex items-center gap-2">
        {/* Music Toggle Button */}
        <button
          onClick={toggleMusic}
          className={`p-3 rounded-lg transition-all duration-200 ${
            isMusicEnabled
              ? 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30 shadow-lg shadow-yellow-500/20'
              : 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30'
          }`}
          title={isMusicEnabled ? 'Turn off casino jazz music' : 'Click to play casino jazz music'}
          disabled={!isMusicLoaded}
        >
          {!isMusicLoaded ? (
            <div className="w-6 h-6 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
          ) : isMusicEnabled ? (
            <Music className="w-6 h-6" />
          ) : (
            <VolumeX className="w-6 h-6" />
          )}
        </button>

        {/* Music Status Text */}
        {!isMusicEnabled && isMusicLoaded && (
          <div className="text-xs text-gray-400 max-w-[120px]">
            <div className="font-semibold">🎵 Casino Music</div>
            <div>Click to play</div>
          </div>
        )}
      </div>

      {/* Volume Control */}
      {isMusicEnabled && (
        <div className="flex items-center gap-2 bg-black/60 rounded-lg p-2">
          <Volume2 className="w-4 h-4 text-gray-400" />
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={volume}
            onChange={(e) => adjustVolume(parseFloat(e.target.value))}
            className="w-16 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
            title={`Volume: ${Math.round(volume * 100)}%`}
          />
          <span className="text-xs text-gray-400 min-w-[2rem]">
            {Math.round(volume * 100)}%
          </span>
        </div>
      )}

      {/* Music Status Indicator */}
      {isMusicEnabled && isPlaying && (
        <div className="flex items-center gap-1">
          <div className="w-1 h-3 bg-yellow-400 rounded-full animate-pulse" />
          <div className="w-1 h-2 bg-yellow-400 rounded-full animate-pulse delay-100" />
          <div className="w-1 h-4 bg-yellow-400 rounded-full animate-pulse delay-200" />
        </div>
      )}

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #fbbf24;
          cursor: pointer;
        }
        
        .slider::-moz-range-thumb {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #fbbf24;
          cursor: pointer;
          border: none;
        }
      `}</style>
    </div>
  );
};

// Hook for using audio manager in other components
export const useAudioManager = () => {
  const [audioManager, setAudioManager] = useState<{
    playSpinSound: () => void;
    playWinSound: () => void;
  } | null>(null);

  const registerAudioManager = (manager: any) => {
    setAudioManager(manager);
  };

  return {
    audioManager,
    registerAudioManager,
    playSpinSound: () => audioManager?.playSpinSound(),
    playWinSound: () => audioManager?.playWinSound(),
  };
};

export default AudioManager;
